# CryptoSignalBot Environment Configuration
# Copy this file to .env and fill in your values

# Exchange Configuration (Legacy - for CCXT)
EXCHANGE=binance
API_KEY=your_api_key_here
API_SECRET=your_api_secret_here
SANDBOX_MODE=True

# Oanda API Configuration (Primary)
# The access token is already set in config.py
# OANDA_ACCESS_TOKEN=your_oanda_access_token_here
OANDA_ACCOUNT_ID=your_oanda_account_id_here
OANDA_ENVIRONMENT=practice

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/cryptosignalbot.log

# Optional: Notification Settings (for future implementation)
# TELEGRAM_BOT_TOKEN=your_telegram_bot_token
# TELEGRAM_CHAT_ID=your_telegram_chat_id
# EMAIL_SMTP_SERVER=smtp.gmail.com
# EMAIL_SMTP_PORT=587
# EMAIL_USERNAME=<EMAIL>
# EMAIL_PASSWORD=your_email_password
