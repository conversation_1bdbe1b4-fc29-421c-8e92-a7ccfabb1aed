"""
Comprehensive Backtesting System
User-friendly CLI interface for strategy backtesting
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
from typing import Dict, List, Tuple
from trading_bot import CryptoTradingBot, Colors

class BacktestSystem:
    """Comprehensive backtesting system with CLI interface"""
    
    def __init__(self):
        """Initialize the backtest system"""
        self.bot = CryptoTradingBot()
        self.pairs = self.bot.pairs
        
        print(f"{Colors.BG_BLUE}{Colors.WHITE}{Colors.BOLD}")
        print("  ╔══════════════════════════════════════════════════════════════╗")
        print("  ║                 🧪 BACKTEST SYSTEM 🧪                       ║")
        print("  ║              Strategy Performance Analysis                   ║")
        print("  ╚══════════════════════════════════════════════════════════════╝")
        print(f"{Colors.END}")
    
    def get_user_input(self) -> Tuple[List[str], datetime, datetime]:
        """Get user input for backtesting parameters"""
        print(f"\n{Colors.CYAN}{Colors.BOLD}📊 Available Crypto Pairs:{Colors.END}")
        
        # Display pairs with numbers
        for i, pair in enumerate(self.pairs, 1):
            print(f"  {Colors.WHITE}{i:2d}. {Colors.YELLOW}{pair}{Colors.END}")
        
        # Get pair selection
        while True:
            try:
                pair_input = input(f"\n{Colors.CYAN}Select crypto pairs (comma-separated numbers, e.g., 1,3,5): {Colors.END}").strip()
                
                if not pair_input:
                    print(f"{Colors.RED}❌ Please enter at least one pair number{Colors.END}")
                    continue
                
                pair_indices = [int(x.strip()) - 1 for x in pair_input.split(',')]
                
                # Validate indices
                if any(i < 0 or i >= len(self.pairs) for i in pair_indices):
                    print(f"{Colors.RED}❌ Invalid pair number. Please use numbers 1-{len(self.pairs)}{Colors.END}")
                    continue
                
                selected_pairs = [self.pairs[i] for i in pair_indices]
                break
                
            except ValueError:
                print(f"{Colors.RED}❌ Invalid input. Please enter numbers separated by commas{Colors.END}")
        
        # Get start time
        while True:
            try:
                start_input = input(f"{Colors.CYAN}Enter Start Time (e.g., \"01-12-2023 00:00\"): {Colors.END}").strip()
                start_time = datetime.strptime(start_input, "%d-%m-%Y %H:%M")
                break
            except ValueError:
                print(f"{Colors.RED}❌ Invalid date format. Please use DD-MM-YYYY HH:MM{Colors.END}")

        # Get end time
        while True:
            try:
                end_input = input(f"{Colors.CYAN}Enter End Time (e.g., \"10-12-2023 23:59\"): {Colors.END}").strip()
                end_time = datetime.strptime(end_input, "%d-%m-%Y %H:%M")

                if end_time <= start_time:
                    print(f"{Colors.RED}❌ End time must be after start time{Colors.END}")
                    continue

                break
            except ValueError:
                print(f"{Colors.RED}❌ Invalid date format. Please use DD-MM-YYYY HH:MM{Colors.END}")
        
        return selected_pairs, start_time, end_time
    
    def simulate_trades(self, df: pd.DataFrame, symbol: str) -> Dict:
        """Simulate trades on historical data"""
        trades = []
        total_trades = 0
        wins = 0
        losses = 0
        points_gained = 0.0
        points_lost = 0.0
        
        # Analyze each candle for signals
        for i in range(100, len(df)):  # Start after enough data for indicators
            # Get data slice for analysis
            df_slice = df.iloc[:i+1]
            
            # Get M15 and M5 data (simulate by resampling)
            df_15m = df_slice.resample('15min').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            
            if len(df_15m) < 50:
                continue
            
            # Analyze for signal
            if self.bot.strategy:
                analysis = self.bot.strategy.analyze_pair(df_15m.tail(100), df_slice.tail(200))
                
                if analysis.get('signal', 0) != 0:
                    # Signal detected - simulate trade
                    entry_price = analysis.get('entry_price', df_slice['close'].iloc[-1])
                    stop_loss = analysis.get('stop_loss', entry_price * 0.98)
                    take_profit = analysis.get('take_profit', entry_price * 1.02)
                    direction = analysis.get('direction', 'LONG')
                    
                    # Look ahead to see if SL or TP was hit
                    trade_result = self.check_trade_outcome(
                        df, i, entry_price, stop_loss, take_profit, direction
                    )
                    
                    if trade_result:
                        trades.append(trade_result)
                        total_trades += 1
                        
                        if trade_result['outcome'] == 'WIN':
                            wins += 1
                            points_gained += trade_result['points']
                        else:
                            losses += 1
                            points_lost += abs(trade_result['points'])
        
        return {
            'total_trades': total_trades,
            'wins': wins,
            'losses': losses,
            'points_gained': points_gained,
            'points_lost': points_lost,
            'net_pnl': points_gained - points_lost,
            'trades': trades
        }
    
    def check_trade_outcome(self, df: pd.DataFrame, entry_index: int, entry_price: float, 
                          stop_loss: float, take_profit: float, direction: str) -> Dict:
        """Check if trade hits SL or TP in future candles"""
        # Look ahead up to 50 candles (or end of data)
        end_index = min(entry_index + 50, len(df) - 1)
        
        for i in range(entry_index + 1, end_index + 1):
            candle = df.iloc[i]
            
            if direction == 'LONG':
                # Check if stop loss hit
                if candle['low'] <= stop_loss:
                    points = stop_loss - entry_price
                    return {
                        'entry_price': entry_price,
                        'exit_price': stop_loss,
                        'points': points,
                        'outcome': 'LOSS',
                        'exit_reason': 'Stop Loss'
                    }
                
                # Check if take profit hit
                if candle['high'] >= take_profit:
                    points = take_profit - entry_price
                    return {
                        'entry_price': entry_price,
                        'exit_price': take_profit,
                        'points': points,
                        'outcome': 'WIN',
                        'exit_reason': 'Take Profit'
                    }
            
            else:  # SHORT
                # Check if stop loss hit
                if candle['high'] >= stop_loss:
                    points = entry_price - stop_loss
                    return {
                        'entry_price': entry_price,
                        'exit_price': stop_loss,
                        'points': points,
                        'outcome': 'LOSS',
                        'exit_reason': 'Stop Loss'
                    }
                
                # Check if take profit hit
                if candle['low'] <= take_profit:
                    points = entry_price - take_profit
                    return {
                        'entry_price': entry_price,
                        'exit_price': take_profit,
                        'points': points,
                        'outcome': 'WIN',
                        'exit_reason': 'Take Profit'
                    }
        
        # No SL or TP hit - close at market (simplified)
        final_price = df.iloc[end_index]['close']
        if direction == 'LONG':
            points = final_price - entry_price
        else:
            points = entry_price - final_price
        
        outcome = 'WIN' if points > 0 else 'LOSS'
        
        return {
            'entry_price': entry_price,
            'exit_price': final_price,
            'points': points,
            'outcome': outcome,
            'exit_reason': 'Market Close'
        }
    
    def display_backtest_result(self, symbol: str, result: Dict):
        """Display backtest result in the requested format"""
        total_trades = result['total_trades']
        wins = result['wins']
        losses = result['losses']
        points_gained = result['points_gained']
        points_lost = result['points_lost']
        net_pnl = result['net_pnl']
        
        print(f"\n{Colors.BG_BLUE}{Colors.WHITE}{Colors.BOLD}[{symbol} Backtest]{Colors.END}")
        print(f"{Colors.WHITE}- Total Trades: {Colors.YELLOW}{total_trades}{Colors.END}")
        print(f"{Colors.WHITE}- Wins: {Colors.GREEN}{wins}{Colors.END}")
        print(f"{Colors.WHITE}- Losses: {Colors.RED}{losses}{Colors.END}")
        print(f"{Colors.WHITE}- Points Gained: {Colors.GREEN}+{points_gained:.3f}{Colors.END}")
        print(f"{Colors.WHITE}- Points Lost: {Colors.RED}-{points_lost:.3f}{Colors.END}")

        if net_pnl > 0:
            print(f"{Colors.WHITE}- Net P/L: {Colors.GREEN}+{net_pnl:.3f} ✅ PROFIT{Colors.END}")
        else:
            print(f"{Colors.WHITE}- Net P/L: {Colors.RED}{net_pnl:.3f} ❌ LOSS{Colors.END}")
    
    def run_backtest(self):
        """Run the complete backtesting process"""
        try:
            # Get user input
            selected_pairs, start_time, end_time = self.get_user_input()
            
            # Display configuration
            print(f"\n{Colors.GREEN}{Colors.BOLD}✅ Backtest Configuration:{Colors.END}")
            print(f"{Colors.WHITE}📊 Pairs: {Colors.CYAN}{', '.join(selected_pairs)}{Colors.END}")
            print(f"{Colors.WHITE}📅 Period: {Colors.CYAN}{start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%Y-%m-%d %H:%M')}{Colors.END}")
            
            duration = end_time - start_time
            print(f"{Colors.WHITE}⏱️ Duration: {Colors.CYAN}{duration.days} days{Colors.END}")
            
            print(f"\n{Colors.YELLOW}🚀 Starting backtest...{Colors.END}")
            print(f"{Colors.BLUE}{'='*60}{Colors.END}")
            
            total_results = []
            
            # Run backtest for each selected pair
            for i, symbol in enumerate(selected_pairs, 1):
                print(f"\n{Colors.CYAN}🔍 Backtesting {symbol} ({i}/{len(selected_pairs)})...{Colors.END}")
                
                # Fetch historical 5M data
                df = self.bot.fetch_historical_data(symbol, '5m', start_time, end_time)
                
                if df.empty:
                    print(f"{Colors.RED}❌ No data available for {symbol}{Colors.END}")
                    continue
                
                print(f"{Colors.WHITE}📊 Analyzing {len(df)} M5 candles...{Colors.END}")
                
                # Run simulation
                result = self.simulate_trades(df, symbol)
                total_results.append((symbol, result))
                
                # Display result
                self.display_backtest_result(symbol, result)
                
                # Small delay
                time.sleep(0.5)
            
            # Display summary
            self.display_backtest_summary(total_results)
            
        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}🛑 Backtest interrupted by user{Colors.END}")
        except Exception as e:
            print(f"\n{Colors.RED}❌ Backtest error: {e}{Colors.END}")
    
    def display_backtest_summary(self, results: List[Tuple[str, Dict]]):
        """Display overall backtest summary"""
        if not results:
            return
        
        total_trades = sum(result[1]['total_trades'] for result in results)
        total_wins = sum(result[1]['wins'] for result in results)
        total_losses = sum(result[1]['losses'] for result in results)
        total_points_gained = sum(result[1]['points_gained'] for result in results)
        total_points_lost = sum(result[1]['points_lost'] for result in results)
        total_net_pnl = sum(result[1]['net_pnl'] for result in results)
        
        print(f"\n{Colors.BG_CYAN}{Colors.WHITE}{Colors.BOLD}")
        print("╔══════════════════════════════════════════════════════════════╗")
        print("║                    📊 BACKTEST SUMMARY 📊                   ║")
        print("╚══════════════════════════════════════════════════════════════╝")
        print(f"{Colors.END}")

        print(f"{Colors.WHITE}📊 Total Pairs Tested: {Colors.CYAN}{len(results)}{Colors.END}")
        print(f"{Colors.WHITE}📈 Total Trades: {Colors.YELLOW}{total_trades}{Colors.END}")
        print(f"{Colors.WHITE}✅ Total Wins: {Colors.GREEN}{total_wins}{Colors.END}")
        print(f"{Colors.WHITE}❌ Total Losses: {Colors.RED}{total_losses}{Colors.END}")

        if total_trades > 0:
            win_rate = (total_wins / total_trades) * 100
            print(f"{Colors.WHITE}📊 Win Rate: {Colors.MAGENTA}{win_rate:.1f}%{Colors.END}")

        print(f"{Colors.WHITE}💰 Total Points Gained: {Colors.GREEN}+{total_points_gained:.3f}{Colors.END}")
        print(f"{Colors.WHITE}💸 Total Points Lost: {Colors.RED}-{total_points_lost:.3f}{Colors.END}")

        if total_net_pnl > 0:
            print(f"{Colors.WHITE}🎯 Overall P/L: {Colors.GREEN}+{total_net_pnl:.3f} ✅ PROFIT{Colors.END}")
        else:
            print(f"{Colors.WHITE}🎯 Overall P/L: {Colors.RED}{total_net_pnl:.3f} ❌ LOSS{Colors.END}")

        print(f"\n{Colors.BLUE}")
        print("╔══════════════════════════════════════════════════════════════╗")
        print("║                      BACKTEST COMPLETE                      ║")
        print("╚══════════════════════════════════════════════════════════════╝")
        print(f"{Colors.END}")

def main():
    """Main function to run backtest system"""
    backtest = BacktestSystem()
    backtest.run_backtest()

if __name__ == "__main__":
    main()
