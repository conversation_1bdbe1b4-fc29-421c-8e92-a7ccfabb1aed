"""
Main Launcher for Crypto Trading Bot
Real-time analysis + Comprehensive backtesting
"""
import sys
from trading_bot import CryptoTradingBot, Colors
from backtest_system import BacktestSystem

def print_main_menu():
    """Print the main menu"""
    print(f"{Colors.CLEAR}")
    print(f"{Colors.BG_BLUE}{Colors.WHITE}{Colors.BOLD}")
    print("  ╔══════════════════════════════════════════════════════════════╗")
    print("  ║                🚀 CRYPTO TRADING BOT 🚀                     ║")
    print("  ║              Advanced Trading System v2.0                    ║")
    print("  ╚══════════════════════════════════════════════════════════════╝")
    print(f"{Colors.END}")
    
    print(f"{Colors.CYAN}{Colors.BOLD}📊 Available Features:{Colors.END}")
    print(f"  {Colors.GREEN}1.{Colors.END} {Colors.WHITE}Real-time Analysis{Colors.END} - {Colors.YELLOW}Analyze all pairs every minute{Colors.END}")
    print(f"  {Colors.GREEN}2.{Colors.END} {Colors.WHITE}Backtest System{Colors.END} - {Colors.YELLOW}Test strategy on historical data{Colors.END}")
    print(f"  {Colors.GREEN}3.{Colors.END} {Colors.WHITE}Exit{Colors.END} - {Colors.YELLOW}Close the application{Colors.END}")
    
    print(f"\n{Colors.BLUE}{'='*70}{Colors.END}")

def show_real_time_info():
    """Show real-time analysis information"""
    print(f"\n{Colors.CYAN}{Colors.BOLD}📊 Real-time Analysis Features:{Colors.END}")
    print(f"  {Colors.WHITE}• Analysis Frequency: {Colors.YELLOW}Every minute (e.g., 13:45, 13:46){Colors.END}")
    print(f"  {Colors.WHITE}• Monitored Pairs: {Colors.YELLOW}20 crypto pairs{Colors.END}")
    print(f"  {Colors.WHITE}• Timeframes: {Colors.YELLOW}1M, 5M, 15M{Colors.END}")
    print(f"  {Colors.WHITE}• Signal Format: {Colors.YELLOW}Entry/SL/TP/Direction/Confidence/Reason{Colors.END}")
    print(f"  {Colors.WHITE}• No Signal Format: {Colors.YELLOW}Price/Bias/Potential Entry{Colors.END}")
    
    print(f"\n{Colors.YELLOW}⚠️ Real-time Analysis will:{Colors.END}")
    print(f"  {Colors.WHITE}• Fetch live data from Binance API{Colors.END}")
    print(f"  {Colors.WHITE}• Run analysis at minute marks (e.g., 13:45:00){Colors.END}")
    print(f"  {Colors.WHITE}• Display colorful results for all 20 pairs{Colors.END}")
    print(f"  {Colors.WHITE}• Continue until you press Ctrl+C{Colors.END}")

def show_backtest_info():
    """Show backtest system information"""
    print(f"\n{Colors.CYAN}{Colors.BOLD}🧪 Backtest System Features:{Colors.END}")
    print(f"  {Colors.WHITE}• Pair Selection: {Colors.YELLOW}Choose specific pairs to test{Colors.END}")
    print(f"  {Colors.WHITE}• Time Range: {Colors.YELLOW}Custom start and end dates{Colors.END}")
    print(f"  {Colors.WHITE}• Data Source: {Colors.YELLOW}Historical 5M data from Binance{Colors.END}")
    print(f"  {Colors.WHITE}• Strategy: {Colors.YELLOW}Full multi-timeframe analysis{Colors.END}")
    print(f"  {Colors.WHITE}• Results: {Colors.YELLOW}Trades/Wins/Losses/Points/P&L{Colors.END}")
    
    print(f"\n{Colors.YELLOW}📊 Backtest Output Example:{Colors.END}")
    print(f"  {Colors.BG_BLUE}{Colors.WHITE}[BTC/USDT Backtest]{Colors.END}")
    print(f"  {Colors.WHITE}- Total Trades: {Colors.YELLOW}17{Colors.END}")
    print(f"  {Colors.WHITE}- Wins: {Colors.GREEN}12{Colors.END}")
    print(f"  {Colors.WHITE}- Losses: {Colors.RED}5{Colors.END}")
    print(f"  {Colors.WHITE}- Points Gained: {Colors.GREEN}+1035{Colors.END}")
    print(f"  {Colors.WHITE}- Points Lost: {Colors.RED}-285{Colors.END}")
    print(f"  {Colors.WHITE}- Net P/L: {Colors.GREEN}+750 ✅ PROFIT{Colors.END}")

def main():
    """Main function with menu system"""
    while True:
        try:
            print_main_menu()
            
            choice = input(f"{Colors.CYAN}Select option (1-3): {Colors.END}").strip()
            
            if choice == '1':
                # Real-time Analysis
                show_real_time_info()
                
                confirm = input(f"\n{Colors.YELLOW}Start real-time analysis? (y/n): {Colors.END}").strip().lower()
                
                if confirm in ['y', 'yes']:
                    print(f"\n{Colors.GREEN}🚀 Starting Real-time Analysis...{Colors.END}")
                    bot = CryptoTradingBot()
                    bot.start_real_time_analysis()
                else:
                    print(f"{Colors.YELLOW}Real-time analysis cancelled.{Colors.END}")
                    continue
            
            elif choice == '2':
                # Backtest System
                show_backtest_info()
                
                confirm = input(f"\n{Colors.YELLOW}Start backtest system? (y/n): {Colors.END}").strip().lower()
                
                if confirm in ['y', 'yes']:
                    print(f"\n{Colors.GREEN}🧪 Starting Backtest System...{Colors.END}")
                    backtest = BacktestSystem()
                    backtest.run_backtest()
                else:
                    print(f"{Colors.YELLOW}Backtest cancelled.{Colors.END}")
                    continue
            
            elif choice == '3':
                # Exit
                print(f"\n{Colors.GREEN}👋 Thank you for using Crypto Trading Bot!{Colors.END}")
                print(f"{Colors.CYAN}🚀 Happy Trading!{Colors.END}")
                sys.exit(0)
            
            else:
                print(f"\n{Colors.RED}❌ Invalid choice. Please select 1, 2, or 3.{Colors.END}")
                input(f"{Colors.YELLOW}Press Enter to continue...{Colors.END}")
                continue
            
            # Ask if user wants to return to menu
            print(f"\n{Colors.BLUE}{'='*50}{Colors.END}")
            return_to_menu = input(f"{Colors.CYAN}Return to main menu? (y/n): {Colors.END}").strip().lower()
            
            if return_to_menu not in ['y', 'yes']:
                print(f"\n{Colors.GREEN}👋 Goodbye!{Colors.END}")
                break
        
        except KeyboardInterrupt:
            print(f"\n\n{Colors.YELLOW}👋 Goodbye!{Colors.END}")
            break
        
        except Exception as e:
            print(f"\n{Colors.RED}❌ Error: {e}{Colors.END}")
            input(f"{Colors.YELLOW}Press Enter to continue...{Colors.END}")

if __name__ == "__main__":
    main()
