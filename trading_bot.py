"""
Advanced Crypto Trading Bot
Real-time analysis every minute + Comprehensive backtesting
"""
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import schedule
import threading
from typing import Dict, List, Optional, Tuple
import logging

# Color codes for beautiful CLI
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    BG_GREEN = '\033[102m'
    BG_RED = '\033[101m'
    BG_BLUE = '\033[104m'
    BG_CYAN = '\033[106m'
    END = '\033[0m'
    CLEAR = '\033[2J\033[H'

# Import strategy modules
try:
    from multi_timeframe_strategy import MultiTimeframeStrategy
except ImportError:
    try:
        from smc_strategy import MultiTimeframeStrategy
    except ImportError:
        MultiTimeframeStrategy = None

class CryptoTradingBot:
    """Advanced Crypto Trading Bot with minute-by-minute analysis"""
    
    def __init__(self):
        """Initialize the trading bot"""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Binance API configuration
        self.api_key = "wPm5zmEkpzQVvnUWSbsaqmDsLcMrKj5z4Bteqx5QnT3tGmtCCCcXOPgGHhV10Hqs"
        self.secret_key = "gBEAZ7W8yjcdr2cRBgCvJekBaQ7A3Qyw72AlSzNDdSWuAOYiW7nnXrepX84oEWxe"
        self.base_url = "https://api.binance.com"
        
        # Trading pairs (20 pairs)
        self.pairs = [
            'ADA/USDT', 'XRP/USDT', 'AVAX/USDT', 'MATIC/USDT', 'LTC/USDT',
            'DOGE/USDT', 'SHIB/USDT', 'BNB/USDT', 'ARB/USDT', 'APT/USDT',
            'OP/USDT', 'INJ/USDT', 'LINK/USDT', 'ATOM/USDT', 'FIL/USDT',
            'SAND/USDT', 'GALA/USDT', 'NEAR/USDT', 'ALGO/USDT', 'AAVE/USDT'
        ]
        
        # Strategy engine
        if MultiTimeframeStrategy:
            self.strategy = MultiTimeframeStrategy()
        else:
            self.strategy = None
        
        # Statistics
        self.total_analyses = 0
        self.signals_found = 0
        self.start_time = datetime.now()
        
        # Bot state
        self.is_running = False
        
        self.print_startup_banner()
    
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('trading_bot.log'),
                logging.StreamHandler()
            ]
        )
    
    def print_startup_banner(self):
        """Print startup banner"""
        print(f"{Colors.CLEAR}")
        print(f"{Colors.BG_BLUE}{Colors.WHITE}{Colors.BOLD}")
        print("  ╔══════════════════════════════════════════════════════════════╗")
        print("  ║                🚀 CRYPTO TRADING BOT 🚀                     ║")
        print("  ║              Real-time + Backtesting System                  ║")
        print("  ╚══════════════════════════════════════════════════════════════╝")
        print(f"{Colors.END}")
        
        print(f"{Colors.CYAN}{Colors.BOLD}📊 Configuration:{Colors.END}")
        print(f"  {Colors.WHITE}• Pairs: {Colors.YELLOW}{len(self.pairs)} crypto pairs{Colors.END}")
        print(f"  {Colors.WHITE}• Analysis: {Colors.YELLOW}Every minute (e.g., 13:45){Colors.END}")
        print(f"  {Colors.WHITE}• Timeframes: {Colors.YELLOW}1M, 5M, 15M{Colors.END}")
        print(f"  {Colors.WHITE}• Features: {Colors.YELLOW}Real-time + Backtesting{Colors.END}")
        print(f"{Colors.BLUE}{'='*70}{Colors.END}")
    
    def convert_symbol_to_binance(self, symbol: str) -> str:
        """Convert symbol format to Binance format"""
        return symbol.replace('/', '').upper()
    
    def fetch_ohlcv_data(self, symbol: str, timeframe: str, limit: int = 100) -> pd.DataFrame:
        """Fetch OHLCV data from Binance"""
        try:
            binance_symbol = self.convert_symbol_to_binance(symbol)
            
            url = f"{self.base_url}/api/v3/klines"
            params = {
                'symbol': binance_symbol,
                'interval': timeframe,
                'limit': limit
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if not data:
                    return pd.DataFrame()
                
                # Convert to DataFrame
                df_data = []
                for kline in data:
                    df_data.append({
                        'timestamp': pd.to_datetime(int(kline[0]), unit='ms'),
                        'open': float(kline[1]),
                        'high': float(kline[2]),
                        'low': float(kline[3]),
                        'close': float(kline[4]),
                        'volume': float(kline[5])
                    })
                
                df = pd.DataFrame(df_data)
                df.set_index('timestamp', inplace=True)
                df = df.sort_index()
                
                return df
            else:
                self.logger.error(f"API error for {symbol}: {response.status_code}")
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()
    
    def fetch_historical_data(self, symbol: str, timeframe: str, start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """Fetch historical data for backtesting"""
        try:
            binance_symbol = self.convert_symbol_to_binance(symbol)
            
            # Convert to milliseconds
            start_ms = int(start_time.timestamp() * 1000)
            end_ms = int(end_time.timestamp() * 1000)
            
            all_data = []
            current_start = start_ms
            
            # Fetch data in chunks (Binance limit is 1000 candles per request)
            while current_start < end_ms:
                url = f"{self.base_url}/api/v3/klines"
                params = {
                    'symbol': binance_symbol,
                    'interval': timeframe,
                    'startTime': current_start,
                    'endTime': end_ms,
                    'limit': 1000
                }
                
                response = requests.get(url, params=params, timeout=10)
                
                if response.status_code != 200:
                    break
                
                data = response.json()
                if not data:
                    break
                
                all_data.extend(data)
                current_start = int(data[-1][0]) + 1
                
                # Rate limiting
                time.sleep(0.1)
            
            if not all_data:
                return pd.DataFrame()
            
            # Convert to DataFrame
            df_data = []
            for kline in all_data:
                df_data.append({
                    'timestamp': pd.to_datetime(int(kline[0]), unit='ms'),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5])
                })
            
            df = pd.DataFrame(df_data)
            df.set_index('timestamp', inplace=True)
            df = df.sort_index()
            
            # Filter to exact time range
            df = df[(df.index >= start_time) & (df.index <= end_time)]
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error fetching historical data for {symbol}: {e}")
            return pd.DataFrame()
    
    def analyze_pair(self, symbol: str) -> Dict:
        """Analyze a single pair for trading signals"""
        try:
            # Fetch data for multiple timeframes
            df_15m = self.fetch_ohlcv_data(symbol, '15m', 100)
            df_5m = self.fetch_ohlcv_data(symbol, '5m', 200)
            df_1m = self.fetch_ohlcv_data(symbol, '1m', 100)
            
            if df_15m.empty or df_5m.empty or df_1m.empty:
                return {
                    'symbol': symbol,
                    'signal': False,
                    'current_price': 0,
                    'bias': 'NEUTRAL',
                    'potential_entry': 'No clear level',
                    'error': 'No data available'
                }
            
            current_price = df_5m['close'].iloc[-1]
            
            # Use strategy if available
            if self.strategy:
                analysis = self.strategy.analyze_pair(df_15m, df_5m)
                
                if analysis.get('signal', 0) != 0:
                    # Signal detected
                    return {
                        'symbol': symbol,
                        'signal': True,
                        'entry_price': analysis.get('entry_price', current_price),
                        'stop_loss': analysis.get('stop_loss', current_price * 0.98),
                        'take_profit': analysis.get('take_profit', current_price * 1.02),
                        'direction': analysis.get('direction', 'LONG'),
                        'confidence': analysis.get('confidence', 50),
                        'reason': self.generate_reason(analysis),
                        'timeframe': analysis.get('timeframe', 'M5'),
                        'current_price': current_price
                    }
                else:
                    # No signal
                    return {
                        'symbol': symbol,
                        'signal': False,
                        'current_price': current_price,
                        'bias': analysis.get('bias', 'NEUTRAL'),
                        'potential_entry': self.get_potential_entry(analysis, current_price),
                        'message': 'No entry currently.'
                    }
            else:
                # Basic analysis without strategy
                return self.basic_analysis(symbol, df_5m, current_price)
                
        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {e}")
            return {
                'symbol': symbol,
                'signal': False,
                'current_price': 0,
                'bias': 'NEUTRAL',
                'potential_entry': 'No clear level',
                'error': str(e)
            }
    
    def generate_reason(self, analysis: Dict) -> str:
        """Generate trading reason from SMC analysis"""
        reasons = []

        # Check for SMC confirmations
        structure = analysis.get('structure_analysis', {})
        entry = analysis.get('entry_analysis', {})
        zones = analysis.get('zones_analysis', {})

        # BOS/CHoCH confirmation
        if structure.get('bos_choch', False):
            reasons.append("BOS/CHoCH")

        # Order Block confirmation
        if zones.get('order_blocks', []):
            reasons.append("Order Block")

        # Entry signal confirmations
        entry_signals = entry.get('entry_signals', [])
        for signal in entry_signals:
            signal_type = signal.get('type', '')
            if 'engulfing' in signal_type:
                reasons.append("Engulfing")
            elif 'pin_bar' in signal_type:
                reasons.append("Pin Bar")
            elif 'zone_interaction' in signal_type:
                reasons.append("Zone Tap")
            elif 'momentum' in signal_type:
                reasons.append("Momentum")

        # Structure strength
        if structure.get('structure_strength', 0) > 70:
            reasons.append("Strong Structure")

        if not reasons:
            reasons = ["SMC Setup"]

        return " + ".join(reasons)
    
    def get_potential_entry(self, analysis: Dict, current_price: float) -> str:
        """Get potential entry level from SMC analysis"""
        try:
            # Check for order blocks
            zones = analysis.get('zones_analysis', {})
            order_blocks = zones.get('order_blocks', [])

            if order_blocks:
                # Find closest order block
                closest_ob = min(order_blocks,
                               key=lambda x: abs(x['level'] - current_price))
                return f"${closest_ob['level']:.4f}"

            # Check for liquidity zones
            liquidity_zones = zones.get('liquidity_zones', [])
            if liquidity_zones:
                # Find closest liquidity zone
                closest_zone = min(liquidity_zones,
                                 key=lambda x: abs(x['level'] - current_price))
                return f"${closest_zone['level']:.4f}"

            # Check for structure levels
            structure = analysis.get('structure_analysis', {})
            if structure.get('bias') == 'BULLISH' and 'recent_low' in structure:
                return f"${structure['recent_low']:.4f}"
            elif structure.get('bias') == 'BEARISH' and 'recent_high' in structure:
                return f"${structure['recent_high']:.4f}"

            return "No clear level"

        except Exception:
            return "No clear level"

    def basic_analysis(self, symbol: str, df: pd.DataFrame, current_price: float) -> Dict:
        """Basic analysis when strategy module is not available"""
        try:
            # Simple moving average analysis
            df['sma_20'] = df['close'].rolling(window=20).mean()
            df['sma_50'] = df['close'].rolling(window=50).mean()

            sma_20 = df['sma_20'].iloc[-1]
            sma_50 = df['sma_50'].iloc[-1]

            # Determine bias
            if current_price > sma_20 > sma_50:
                bias = "BULLISH"
                potential_entry = f"${sma_20:.4f}"
            elif current_price < sma_20 < sma_50:
                bias = "BEARISH"
                potential_entry = f"${sma_20:.4f}"
            else:
                bias = "NEUTRAL"
                potential_entry = "No clear level"

            return {
                'symbol': symbol,
                'signal': False,
                'current_price': current_price,
                'bias': bias,
                'potential_entry': potential_entry,
                'message': 'No entry currently.'
            }

        except Exception:
            return {
                'symbol': symbol,
                'signal': False,
                'current_price': current_price,
                'bias': 'NEUTRAL',
                'potential_entry': 'No clear level',
                'message': 'No entry currently.'
            }

    def display_signal_result(self, result: Dict):
        """Display signal result with colors"""
        symbol = result['symbol']

        if result['signal']:
            # Signal detected
            direction = "Long" if result['direction'] == 'LONG' else "Short"
            color = Colors.GREEN if direction == "Long" else Colors.RED

            print(f"{color}{Colors.BOLD}[{symbol}] {direction.upper()} SIGNAL 🔔{Colors.END}")
            print(f"  {Colors.WHITE}Entry Price: {Colors.YELLOW}${result['entry_price']:.4f}{Colors.END}")
            print(f"  {Colors.WHITE}Stop Loss: {Colors.RED}${result['stop_loss']:.4f}{Colors.END}")
            print(f"  {Colors.WHITE}Take Profit: {Colors.GREEN}${result['take_profit']:.4f}{Colors.END}")
            print(f"  {Colors.WHITE}Direction: {color}{direction}{Colors.END}")
            print(f"  {Colors.WHITE}Confidence: {Colors.CYAN}{result['confidence']}%{Colors.END}")
            print(f"  {Colors.WHITE}Reason: {Colors.MAGENTA}{result['reason']}{Colors.END}")
            print(f"  {Colors.WHITE}Entry Timeframe: {Colors.BLUE}{result['timeframe']}{Colors.END}")

        else:
            # No signal
            bias_color = Colors.GREEN if result['bias'] == 'BULLISH' else Colors.RED if result['bias'] == 'BEARISH' else Colors.YELLOW

            print(f"{Colors.CYAN}[{symbol}]{Colors.END}")
            print(f"  {Colors.WHITE}Current Price: {Colors.YELLOW}${result['current_price']:.4f}{Colors.END}")
            print(f"  {Colors.WHITE}Direction Bias: {bias_color}{result['bias']}{Colors.END}")
            print(f"  {Colors.WHITE}Potential Entry: {Colors.MAGENTA}{result['potential_entry']}{Colors.END}")
            print(f"  {Colors.WHITE}No entry currently.{Colors.END}")

    def analyze_all_pairs(self):
        """Analyze all pairs and display results"""
        current_time = datetime.now().strftime('%H:%M')

        print(f"\n{Colors.BG_BLUE}{Colors.WHITE}{Colors.BOLD} 📊 MARKET ANALYSIS - {current_time} {Colors.END}")
        print(f"{Colors.BLUE}{'='*70}{Colors.END}")

        signals_found = 0

        for i, symbol in enumerate(self.pairs, 1):
            print(f"\n{Colors.CYAN}[{i:2d}/20] Analyzing {symbol}...{Colors.END}")

            result = self.analyze_pair(symbol)
            self.display_signal_result(result)

            if result['signal']:
                signals_found += 1

            self.total_analyses += 1

            # Small delay to avoid rate limiting
            time.sleep(0.1)

        self.signals_found += signals_found

        # Summary
        print(f"\n{Colors.BLUE}{'='*70}{Colors.END}")
        print(f"{Colors.WHITE}📊 Analysis Summary: {Colors.YELLOW}{signals_found} signals found{Colors.END} | "
              f"{Colors.WHITE}Total: {Colors.CYAN}{self.signals_found} signals{Colors.END}")
        print(f"{Colors.BLUE}{'='*70}{Colors.END}")

    def schedule_analysis(self):
        """Schedule analysis to run every minute"""
        # Schedule to run at the start of every minute
        schedule.every().minute.at(":00").do(self.analyze_all_pairs)

        print(f"\n{Colors.GREEN}{Colors.BOLD}⏰ Scheduled analysis every minute{Colors.END}")
        print(f"{Colors.YELLOW}Next analysis at: {(datetime.now() + timedelta(minutes=1)).strftime('%H:%M')}{Colors.END}")

        # Run scheduler
        while self.is_running:
            schedule.run_pending()
            time.sleep(1)

    def start_real_time_analysis(self):
        """Start real-time analysis"""
        print(f"\n{Colors.GREEN}{Colors.BOLD}🚀 Starting Real-time Analysis{Colors.END}")
        print(f"{Colors.CYAN}📊 Analyzing {len(self.pairs)} pairs every minute{Colors.END}")
        print(f"{Colors.YELLOW}⏰ Analysis runs at minute marks (e.g., 13:45, 13:46, etc.){Colors.END}")
        print(f"{Colors.WHITE}Press Ctrl+C to stop{Colors.END}")

        self.is_running = True

        try:
            # Run initial analysis
            self.analyze_all_pairs()

            # Start scheduler in separate thread
            scheduler_thread = threading.Thread(target=self.schedule_analysis, daemon=True)
            scheduler_thread.start()

            # Keep main thread alive
            while self.is_running:
                time.sleep(1)

        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}🛑 Stopping real-time analysis...{Colors.END}")
            self.is_running = False
            self.display_final_stats()

    def display_final_stats(self):
        """Display final statistics"""
        runtime = datetime.now() - self.start_time

        print(f"\n{Colors.BG_CYAN}{Colors.WHITE}{Colors.BOLD} 📊 FINAL STATISTICS 📊 {Colors.END}")
        print(f"{Colors.BLUE}{'='*50}{Colors.END}")
        print(f"{Colors.WHITE}🕐 Runtime: {Colors.CYAN}{str(runtime).split('.')[0]}{Colors.END}")
        print(f"{Colors.WHITE}📊 Total Analyses: {Colors.YELLOW}{self.total_analyses}{Colors.END}")
        print(f"{Colors.WHITE}🎯 Signals Found: {Colors.GREEN}{self.signals_found}{Colors.END}")

        if self.total_analyses > 0:
            signal_rate = (self.signals_found / self.total_analyses) * 100
            print(f"{Colors.WHITE}📈 Signal Rate: {Colors.MAGENTA}{signal_rate:.1f}%{Colors.END}")

        print(f"{Colors.BLUE}{'='*50}{Colors.END}")
        print(f"{Colors.GREEN}👋 Thank you for using Crypto Trading Bot!{Colors.END}")
