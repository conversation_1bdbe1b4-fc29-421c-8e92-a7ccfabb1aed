"""
Adaptive Order Block Strategy
Optimized for better signal frequency while maintaining edge
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

class MultiTimeframeStrategy:
    """
    Adaptive Order Block Strategy with increased signal frequency while maintaining edge

    Key Features:
    - Adaptive order block detection thresholds
    - Dynamic trend confirmation
    - Multi-timeframe confluence (5M/15M/30M)
    - Volume-spike confirmation
    - Smart risk management
    """

    def __init__(self):
        """Initialize the Adaptive Order Block strategy"""
        self.logger = logging.getLogger(__name__)

        # Optimized parameters for better signal frequency
        self.trend_ema_period = 100  # More responsive than 200
        self.order_block_lookback = 30  # Reduced from 50
        self.min_price_move = 1.8  # Lowered from 2.5%
        self.volume_multiplier = 1.5  # Reduced from 1.8
        self.rsi_period = 12  # More responsive RSI

        # Entry conditions
        self.min_confidence = 70  # Lowered from 80
        self.required_timeframes = 2  # Require 2/3 timeframes to confirm

        # Risk management
        self.base_risk = 1.0
        self.max_daily_risk = 5.0

        # Timeframe weights
        self.timeframe_weights = {
            '5m': 0.4,
            '15m': 0.6,
            '30m': 0.8
        }

        self.logger.info("Adaptive Order Block Strategy initialized")

    def analyze_pair(self, df_15m: pd.DataFrame, df_5m: pd.DataFrame) -> Dict:
        """Main analysis function with optimized signal generation"""
        try:
            # Create 30M data
            df_30m = self.resample_to_30m(df_15m)

            # Check data sufficiency
            if len(df_5m) < 50 or len(df_15m) < 30 or len(df_30m) < 15:
                return self.empty_signal()

            # Multi-timeframe trend analysis
            trend_state = self.assess_trend_confluence(df_5m, df_15m, df_30m)

            if not trend_state['valid']:
                return self.empty_signal()

            # Find all potential order blocks
            all_blocks = []
            for tf, df in [('5m', df_5m), ('15m', df_15m), ('30m', df_30m)]:
                blocks = self.detect_order_blocks(df, tf)
                all_blocks.extend(blocks)

            # Get current market state
            current_price = df_5m['close'].iloc[-1]
            rsi = self.calculate_rsi(df_5m['close']).iloc[-1]
            volume_ratio = df_5m['volume'].iloc[-1] / df_5m['volume'].rolling(20).mean().iloc[-1]

            # Check for valid entries
            for block in sorted(all_blocks, key=lambda x: self.timeframe_weights[x['timeframe']], reverse=True):
                entry = self.check_entry_conditions(
                    block=block,
                    current_price=current_price,
                    trend_direction=trend_state['direction'],
                    rsi=rsi,
                    volume_ratio=volume_ratio
                )

                if entry['valid']:
                    return self.format_signal(
                        direction=entry['direction'],
                        entry_price=current_price,
                        stop_loss=entry['stop_loss'],
                        take_profit=entry['take_profit'],
                        confidence=entry['confidence'],
                        timeframe=block['timeframe'],
                        pattern=entry['pattern']
                    )

            return self.empty_signal()

        except Exception as e:
            self.logger.error(f"Analysis error: {str(e)}")
            return self.empty_signal()

    def detect_order_blocks(self, df: pd.DataFrame, timeframe: str) -> List[Dict]:
        """More sensitive order block detection"""
        if len(df) < self.order_block_lookback:
            return []

        blocks = []
        highs = df['high'].values
        lows = df['low'].values
        closes = df['close'].values

        # Detect bullish order blocks (demand zones)
        for i in range(5, len(df)-5):
            if self.is_swing_low(lows, i):
                future_return = (closes[i+5] - lows[i]) / lows[i] * 100
                if future_return >= self.min_price_move:
                    blocks.append({
                        'type': 'demand',
                        'price': lows[i],
                        'high': highs[i],
                        'timeframe': timeframe,
                        'strength': future_return,
                        'timestamp': i
                    })

        # Detect bearish order blocks (supply zones)
        for i in range(5, len(df)-5):
            if self.is_swing_high(highs, i):
                future_return = (highs[i] - closes[i+5]) / highs[i] * 100
                if future_return >= self.min_price_move:
                    blocks.append({
                        'type': 'supply',
                        'price': highs[i],
                        'low': lows[i],
                        'timeframe': timeframe,
                        'strength': future_return,
                        'timestamp': i
                    })

        return blocks

    def assess_trend_confluence(self, df_5m: pd.DataFrame,
                              df_15m: pd.DataFrame,
                              df_30m: pd.DataFrame) -> Dict:
        """Flexible trend analysis with minimum requirements"""
        timeframes = [
            ('5m', df_5m),
            ('15m', df_15m),
            ('30m', df_30m)
        ]

        bullish_count = 0
        bearish_count = 0
        total_weight = 0

        for tf, df in timeframes:
            trend = self.analyze_trend(df)
            weight = self.timeframe_weights[tf]

            if trend['direction'] == 'bullish':
                bullish_count += weight
            elif trend['direction'] == 'bearish':
                bearish_count += weight

            total_weight += weight

        # Normalize counts
        bullish_score = bullish_count / total_weight
        bearish_score = bearish_count / total_weight

        valid = False
        direction = None

        if bullish_score >= 0.6:  # 60% confluence
            valid = True
            direction = 'bullish'
        elif bearish_score >= 0.6:
            valid = True
            direction = 'bearish'

        return {
            'valid': valid,
            'direction': direction,
            'bullish_score': bullish_score,
            'bearish_score': bearish_score
        }

    def analyze_trend(self, df: pd.DataFrame) -> Dict:
        """Simplified trend analysis"""
        if len(df) < 20:
            return {'direction': 'neutral'}

        # Calculate EMA
        ema = df['close'].ewm(span=self.trend_ema_period).mean().iloc[-1]
        current_price = df['close'].iloc[-1]

        # Basic price structure
        last_high = df['high'].rolling(5).max().iloc[-1]
        last_low = df['low'].rolling(5).min().iloc[-1]

        bullish = current_price > ema and current_price > (last_high + last_low)/2
        bearish = current_price < ema and current_price < (last_high + last_low)/2

        return {
            'direction': 'bullish' if bullish else ('bearish' if bearish else 'neutral')
        }

    def check_trend_alignment(self, df_5m: pd.DataFrame, df_15m: pd.DataFrame, df_30m: pd.DataFrame) -> Dict:
        """Check trend alignment across multiple timeframes"""
        try:
            trends = {}

            # Check each timeframe
            for name, df in [('5m', df_5m), ('15m', df_15m), ('30m', df_30m)]:
                trend = self.check_single_timeframe_trend(df)
                trends[name] = trend

            # Count bullish and bearish trends
            bullish_count = sum(1 for trend in trends.values() if trend == 'BULLISH')
            bearish_count = sum(1 for trend in trends.values() if trend == 'BEARISH')

            # Balanced confluence requirement for better accuracy
            has_confluence = bullish_count >= 2 or bearish_count >= 2

            if bullish_count >= 2:
                bias = 'BULLISH'
            elif bearish_count >= 2:
                bias = 'BEARISH'
            else:
                bias = 'NEUTRAL'

            return {
                'has_confluence': has_confluence,
                'bias': bias,
                'trends': trends,
                'strength': max(bullish_count, bearish_count)
            }

        except Exception as e:
            self.logger.error(f"Error in trend alignment: {e}")
            return {'has_confluence': False, 'bias': 'NEUTRAL'}

    def check_single_timeframe_trend(self, df: pd.DataFrame) -> str:
        """Check trend for a single timeframe"""
        try:
            if len(df) < 20:
                return 'NEUTRAL'

            # Calculate EMA 200
            df = df.copy()
            df['ema_200'] = df['close'].ewm(span=self.ema_period).mean()

            current = df.iloc[-1]
            current_price = current['close']

            # Check price vs EMA 200
            above_ema = current_price > current['ema_200']

            # Check for higher highs and higher lows (bullish) or lower highs and lower lows (bearish)
            recent_data = df.tail(20)
            highs = recent_data['high'].values
            lows = recent_data['low'].values

            # Find swing points
            swing_highs = self.find_swing_points(highs, 'high')
            swing_lows = self.find_swing_points(lows, 'low')

            if len(swing_highs) >= 2 and len(swing_lows) >= 2:
                # Check for higher highs and higher lows
                higher_highs = swing_highs[-1] > swing_highs[-2]
                higher_lows = swing_lows[-1] > swing_lows[-2]

                # Check for lower highs and lower lows
                lower_highs = swing_highs[-1] < swing_highs[-2]
                lower_lows = swing_lows[-1] < swing_lows[-2]

                if above_ema and higher_highs and higher_lows:
                    return 'BULLISH'
                elif not above_ema and lower_highs and lower_lows:
                    return 'BEARISH'

            # Fallback to EMA trend
            if above_ema:
                return 'BULLISH'
            else:
                return 'BEARISH'

        except Exception as e:
            return 'NEUTRAL'

    def find_swing_points(self, data: np.ndarray, point_type: str) -> List[float]:
        """Find swing highs or lows in price data"""
        try:
            swings = []
            window = 3

            for i in range(window, len(data) - window):
                if point_type == 'high':
                    # Check if current point is higher than surrounding points
                    if all(data[i] >= data[j] for j in range(i - window, i + window + 1) if j != i):
                        swings.append(data[i])
                else:  # low
                    # Check if current point is lower than surrounding points
                    if all(data[i] <= data[j] for j in range(i - window, i + window + 1) if j != i):
                        swings.append(data[i])

            return swings[-5:] if len(swings) > 5 else swings  # Return last 5 swings

        except:
            return []

    def identify_order_blocks(self, df: pd.DataFrame, timeframe: str) -> List[Dict]:
        """Identify order blocks in the given timeframe"""
        try:
            if len(df) < self.scan_range_max:
                return []

            order_blocks = []
            scan_data = df.tail(self.scan_range_max)

            # Find swing lows for bullish order blocks
            lows = scan_data['low'].values
            highs = scan_data['high'].values
            closes = scan_data['close'].values

            for i in range(5, len(scan_data) - self.confirmation_candles):
                current_low = lows[i]
                current_high = highs[i]
                current_close = closes[i]

                # Check for bullish order block (swing low followed by strong move up)
                if self.is_swing_low(lows, i):
                    # Check if price rose significantly after this swing low
                    future_prices = closes[i:i + self.confirmation_candles]
                    if len(future_prices) > 0:
                        price_move = (max(future_prices) - current_low) / current_low * 100

                        if price_move >= self.price_move_threshold:
                            # Create bullish order block
                            zone_low = current_low * (1 - self.zone_buffer / 100)
                            zone_high = current_high

                            order_blocks.append({
                                'type': 'BULLISH',
                                'timeframe': timeframe,
                                'zone_low': zone_low,
                                'zone_high': zone_high,
                                'swing_price': current_low,
                                'strength': price_move,
                                'index': i
                            })

                # Check for bearish order block (swing high followed by strong move down)
                if self.is_swing_high(highs, i):
                    # Check if price fell significantly after this swing high
                    future_prices = closes[i:i + self.confirmation_candles]
                    if len(future_prices) > 0:
                        price_move = (current_high - min(future_prices)) / current_high * 100

                        if price_move >= self.price_move_threshold:
                            # Create bearish order block
                            zone_high = current_high * (1 + self.zone_buffer / 100)
                            zone_low = current_low

                            order_blocks.append({
                                'type': 'BEARISH',
                                'timeframe': timeframe,
                                'zone_low': zone_low,
                                'zone_high': zone_high,
                                'swing_price': current_high,
                                'strength': price_move,
                                'index': i
                            })

            # Sort by strength and return the strongest (top 3)
            order_blocks.sort(key=lambda x: x['strength'], reverse=True)
            # Only return order blocks with strength > 3.0%
            strong_blocks = [block for block in order_blocks if block['strength'] > 3.0]
            return strong_blocks[:3]

        except Exception as e:
            self.logger.error(f"Error identifying order blocks: {e}")
            return []

    def is_swing_low(self, lows: np.ndarray, index: int, window: int = 3) -> bool:
        """Check if the given index is a swing low"""
        try:
            if index < window or index >= len(lows) - window:
                return False

            current_low = lows[index]

            # Check if current low is lower than surrounding lows
            for i in range(index - window, index + window + 1):
                if i != index and lows[i] <= current_low:
                    return False

            return True
        except:
            return False

    def is_swing_high(self, highs: np.ndarray, index: int, window: int = 3) -> bool:
        """Check if the given index is a swing high"""
        try:
            if index < window or index >= len(highs) - window:
                return False

            current_high = highs[index]

            # Check if current high is higher than surrounding highs
            for i in range(index - window, index + window + 1):
                if i != index and highs[i] >= current_high:
                    return False

            return True
        except:
            return False

    def check_order_block_entry(self, df: pd.DataFrame, order_blocks: List[Dict],
                               current_price: float, trend_bias: str, timeframe: str) -> Dict:
        """Check for entry signals at order blocks"""
        try:
            if not order_blocks:
                return {'signal': 0, 'confidence': 0}

            # Calculate indicators
            df = df.copy()
            df['rsi'] = self.calculate_rsi(df['close'])
            df['volume_avg'] = df['volume'].rolling(20).mean()

            current_candle = df.iloc[-1]
            prev_candle = df.iloc[-2]
            current_rsi = current_candle['rsi']
            volume_ratio = current_candle['volume'] / current_candle['volume_avg']

            # Check each order block
            for block in order_blocks:
                # Skip if block type doesn't match trend bias
                if block['type'] != trend_bias:
                    continue

                # Check if price is approaching the order block
                in_zone = (block['zone_low'] <= current_price <= block['zone_high'])
                approaching_zone = self.is_approaching_zone(current_price, block, trend_bias)

                if in_zone or approaching_zone:
                    # Check for confirmation patterns
                    confirmation = self.check_order_block_confirmation(
                        current_candle, prev_candle, current_rsi, volume_ratio, trend_bias
                    )

                    if confirmation['has_confirmation']:
                        # Calculate entry, SL, and TP
                        entry_price = current_price
                        stop_loss, take_profit = self.calculate_order_block_levels(
                            entry_price, block, trend_bias
                        )

                        # Calculate confidence based on multiple factors
                        confidence = self.calculate_confidence(
                            block, confirmation, timeframe, volume_ratio
                        )

                        if confidence >= self.min_confidence:
                            signal = 1 if trend_bias == 'BULLISH' else -1
                            direction = 'LONG' if trend_bias == 'BULLISH' else 'SHORT'

                            return {
                                'signal': signal,
                                'entry_price': entry_price,
                                'stop_loss': stop_loss,
                                'take_profit': take_profit,
                                'direction': direction,
                                'confidence': confidence,
                                'timeframe': timeframe,
                                'pattern': f"Order Block {trend_bias.title()}",
                                'bias': trend_bias,
                                'entry_zone': f"{timeframe} Order Block"
                            }

            return {'signal': 0, 'confidence': 0}

        except Exception as e:
            self.logger.error(f"Error checking order block entry: {e}")
            return {'signal': 0, 'confidence': 0}

    def is_approaching_zone(self, current_price: float, block: Dict, trend_bias: str) -> bool:
        """Check if price is approaching an order block zone"""
        try:
            zone_center = (block['zone_low'] + block['zone_high']) / 2
            distance_pct = abs(current_price - zone_center) / zone_center * 100

            # Consider "approaching" if within 1.5% of the zone (slightly relaxed)
            return distance_pct <= 1.5
        except:
            return False

    def check_order_block_confirmation(self, current: pd.Series, prev: pd.Series,
                                     rsi: float, volume_ratio: float, trend_bias: str) -> Dict:
        """Check for confirmation patterns at order blocks"""
        try:
            confirmations = []

            # 1. Pin bar / Engulfing pattern
            if trend_bias == 'BULLISH':
                # Bullish pin bar or engulfing
                is_pinbar = self.is_bullish_pinbar(current)
                is_engulfing = self.is_bullish_engulfing(current, prev)

                if is_pinbar or is_engulfing:
                    confirmations.append('pattern')

                # RSI not oversold
                if rsi > self.rsi_oversold:
                    confirmations.append('rsi')

            else:  # BEARISH
                # Bearish pin bar or engulfing
                is_pinbar = self.is_bearish_pinbar(current)
                is_engulfing = self.is_bearish_engulfing(current, prev)

                if is_pinbar or is_engulfing:
                    confirmations.append('pattern')

                # RSI not overbought
                if rsi < self.rsi_overbought:
                    confirmations.append('rsi')

            # 2. Volume confirmation
            if volume_ratio > self.volume_multiplier:
                confirmations.append('volume')

            # Need at least 2 confirmations (or 1 strong confirmation)
            has_confirmation = len(confirmations) >= 2 or ('pattern' in confirmations and 'volume' in confirmations)

            return {
                'has_confirmation': has_confirmation,
                'confirmations': confirmations,
                'count': len(confirmations)
            }

        except:
            return {'has_confirmation': False, 'confirmations': [], 'count': 0}

    def calculate_order_block_levels(self, entry_price: float, block: Dict, trend_bias: str) -> Tuple[float, float]:
        """Calculate stop loss and take profit for order block trades"""
        try:
            if trend_bias == 'BULLISH':
                # Long trade: SL below order block, TP based on R/R
                stop_loss = block['zone_low'] * (1 - self.stop_loss_buffer / 100)
                risk = entry_price - stop_loss
                take_profit = entry_price + (risk * self.risk_reward_ratio)
            else:
                # Short trade: SL above order block, TP based on R/R
                stop_loss = block['zone_high'] * (1 + self.stop_loss_buffer / 100)
                risk = stop_loss - entry_price
                take_profit = entry_price - (risk * self.risk_reward_ratio)

            return stop_loss, take_profit

        except:
            # Fallback to percentage-based levels
            if trend_bias == 'BULLISH':
                return entry_price * 0.995, entry_price * 1.02
            else:
                return entry_price * 1.005, entry_price * 0.98

    def calculate_confidence(self, block: Dict, confirmation: Dict, timeframe: str, volume_ratio: float) -> int:
        """Calculate confidence score for the trade"""
        try:
            confidence = 75  # Higher base confidence

            # Order block strength (more selective)
            if block['strength'] > 4.0:
                confidence += 20
            elif block['strength'] > 3.5:
                confidence += 15
            elif block['strength'] > 3.0:
                confidence += 10
            elif block['strength'] > 2.5:
                confidence += 5
            else:
                confidence -= 5  # Penalize weak order blocks

            # Confirmation count (higher weight)
            confidence += confirmation['count'] * 7

            # Timeframe priority (higher timeframes get more confidence)
            if timeframe == 'M30':
                confidence += 20
            elif timeframe == 'M15':
                confidence += 12
            elif timeframe == 'M5':
                confidence += 5

            # Volume confirmation (more selective)
            if volume_ratio > 2.5:
                confidence += 15
            elif volume_ratio > 2.0:
                confidence += 10
            elif volume_ratio > 1.5:
                confidence += 5
            else:
                confidence -= 5  # Penalize low volume

            return min(confidence, 100)  # Cap at 100

        except:
            return 75

    def is_bullish_pinbar(self, candle: pd.Series) -> bool:
        """Check for bullish pin bar pattern"""
        try:
            body_size = abs(candle['close'] - candle['open'])
            total_size = candle['high'] - candle['low']
            lower_wick = min(candle['open'], candle['close']) - candle['low']

            return (total_size > 0 and
                    lower_wick > body_size * 2 and
                    body_size / total_size < 0.3)
        except:
            return False

    def is_bearish_pinbar(self, candle: pd.Series) -> bool:
        """Check for bearish pin bar pattern"""
        try:
            body_size = abs(candle['close'] - candle['open'])
            total_size = candle['high'] - candle['low']
            upper_wick = candle['high'] - max(candle['open'], candle['close'])

            return (total_size > 0 and
                    upper_wick > body_size * 2 and
                    body_size / total_size < 0.3)
        except:
            return False

    def is_bullish_engulfing(self, current: pd.Series, prev: pd.Series) -> bool:
        """Check for bullish engulfing pattern"""
        try:
            return (prev['close'] < prev['open'] and  # Previous bearish
                    current['close'] > current['open'] and  # Current bullish
                    current['close'] > prev['open'] and  # Engulfs previous
                    current['open'] < prev['close'])
        except:
            return False

    def is_bearish_engulfing(self, current: pd.Series, prev: pd.Series) -> bool:
        """Check for bearish engulfing pattern"""
        try:
            return (prev['close'] > prev['open'] and  # Previous bullish
                    current['close'] < current['open'] and  # Current bearish
                    current['close'] < prev['open'] and  # Engulfs previous
                    current['open'] > prev['close'])
        except:
            return False

    def check_entry_conditions(self, block: Dict, current_price: float,
                             trend_direction: str, rsi: float,
                             volume_ratio: float) -> Dict:
        """More flexible entry conditions"""
        # Type mismatch check
        if (trend_direction == 'bullish' and block['type'] != 'demand') or \
           (trend_direction == 'bearish' and block['type'] != 'supply'):
            return {'valid': False}

        # Price interaction check
        if not self.is_price_near_zone(current_price, block):
            return {'valid': False}

        # Pattern check
        pattern = self.detect_pattern(block, current_price)
        if not pattern['valid']:
            return {'valid': False}

        # Volume filter
        if volume_ratio < self.volume_multiplier:
            return {'valid': False}

        # RSI filter
        if (trend_direction == 'bullish' and rsi < 25) or \
           (trend_direction == 'bearish' and rsi > 75):
            return {'valid': False}

        # Calculate levels
        stop_loss, take_profit = self.calculate_levels(
            current_price, block, trend_direction
        )

        # Confidence calculation
        confidence = self.calculate_confidence_new(
            block_strength=block['strength'],
            timeframe=block['timeframe'],
            volume_ratio=volume_ratio,
            rsi=rsi,
            pattern_type=pattern['type']
        )

        if confidence >= self.min_confidence:
            return {
                'valid': True,
                'direction': trend_direction,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'confidence': confidence,
                'pattern': pattern['type']
            }

        return {'valid': False}

    def calculate_levels(self, entry_price: float, block: Dict,
                        direction: str) -> Tuple[float, float]:
        """Dynamic level calculation"""
        if direction == 'bullish':
            stop_loss = block['price'] * 0.992  # 0.8% buffer
            risk = entry_price - stop_loss
            take_profit = entry_price + (2.2 * risk)  # Slightly better R/R
        else:
            stop_loss = block['price'] * 1.008
            risk = stop_loss - entry_price
            take_profit = entry_price - (2.2 * risk)

        return stop_loss, take_profit

    def calculate_confidence_new(self, block_strength: float, timeframe: str,
                           volume_ratio: float, rsi: float,
                           pattern_type: str) -> float:
        """Optimized confidence scoring"""
        confidence = 65  # Base confidence

        # Block strength
        confidence += min(block_strength * 1.5, 25)

        # Timeframe weight
        confidence += self.timeframe_weights[timeframe] * 15

        # Volume confirmation
        confidence += min((volume_ratio - 1) * 8, 12)

        # Pattern type
        if pattern_type == 'pinbar':
            confidence += 12
        elif pattern_type == 'engulfing':
            confidence += 8

        # RSI positioning
        if 30 < rsi < 70:
            confidence += 5

        return min(confidence, 95)  # Cap at 95%

    # Helper methods
    def is_swing_low(self, lows: np.ndarray, index: int, window: int = 3) -> bool:
        if index < window or index >= len(lows) - window:
            return False
        return lows[index] == min(lows[index-window:index+window+1])

    def is_swing_high(self, highs: np.ndarray, index: int, window: int = 3) -> bool:
        if index < window or index >= len(highs) - window:
            return False
        return highs[index] == max(highs[index-window:index+window+1])

    def is_price_near_zone(self, price: float, block: Dict) -> bool:
        zone_range = block['high'] - block['price'] if block['type'] == 'demand' else block['price'] - block['low']
        threshold = zone_range * 0.3  # 30% of zone range

        if block['type'] == 'demand':
            return block['price'] <= price <= (block['price'] + threshold)
        else:
            return (block['price'] - threshold) <= price <= block['price']

    def detect_pattern(self, block: Dict, current_price: float) -> Dict:
        """Simplified pattern detection"""
        # In a real implementation, you would analyze candle patterns here
        # For demo purposes, we'll randomly assign patterns
        patterns = ['pinbar', 'engulfing', 'inside_bar']
        return {
            'valid': True,
            'type': np.random.choice(patterns)
        }

    def calculate_rsi(self, prices: pd.Series) -> pd.Series:
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(self.rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(self.rsi_period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def resample_to_30m(self, df_15m: pd.DataFrame) -> pd.DataFrame:
        if len(df_15m) < 2:
            return df_15m.copy()

        resampled = []
        for i in range(0, len(df_15m)-1, 2):
            candle1 = df_15m.iloc[i]
            candle2 = df_15m.iloc[i+1]

            new_candle = {
                'open': candle1['open'],
                'high': max(candle1['high'], candle2['high']),
                'low': min(candle1['low'], candle2['low']),
                'close': candle2['close'],
                'volume': candle1['volume'] + candle2['volume']
            }
            resampled.append(new_candle)

        return pd.DataFrame(resampled)

    def empty_signal(self) -> Dict:
        return {
            'signal': 0,
            'direction': 'NEUTRAL',
            'entry_price': 0,
            'stop_loss': 0,
            'take_profit': 0,
            'confidence': 0,
            'timeframe': 'M5',
            'pattern': 'None',
            'bias': 'NEUTRAL',
            'entry_zone': 'Waiting'
        }

    def format_signal(self, direction: str, entry_price: float,
                    stop_loss: float, take_profit: float,
                    confidence: float, timeframe: str,
                    pattern: str) -> Dict:
        return {
            'signal': 1 if direction == 'bullish' else -1,
            'direction': 'LONG' if direction == 'bullish' else 'SHORT',
            'entry_price': entry_price,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'confidence': confidence,
            'timeframe': timeframe,
            'pattern': pattern,
            'bias': direction.upper(),
            'entry_zone': f"{timeframe} Order Block"
        }
