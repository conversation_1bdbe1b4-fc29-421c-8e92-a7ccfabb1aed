"""
High-Accuracy Price Action Strategy
Simple but effective price action patterns with high win rate
Target: 65%+ win rate with consistent profits
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

class MultiTimeframeStrategy:
    """
    High-Accuracy Price Action Strategy

    Strategy Overview:
    1. Simple Price Action Patterns: Proven setups
    2. Support/Resistance Levels: Key levels trading
    3. Candlestick Patterns: High-probability formations
    4. Moving Average Signals: Trend confirmation
    5. Volume Analysis: Strength confirmation
    """

    def __init__(self):
        """Initialize the price action strategy"""
        self.logger = logging.getLogger(__name__)

        # Optimized parameters for higher win rate
        self.min_confidence = 70  # Higher threshold for better quality
        self.risk_reward_min = 2.5  # Better risk reward
        self.risk_reward_max = 4.0  # Maximum RR

        # Stricter price action parameters
        self.support_resistance_strength = 2  # Minimum touches for S/R (more flexible)
        self.breakout_confirmation_candles = 2  # Candles to confirm breakout
        self.volume_multiplier = 1.8  # Higher volume confirmation threshold

        # Technical indicators
        self.rsi_oversold = 30
        self.rsi_overbought = 70
        self.rsi_neutral_low = 40
        self.rsi_neutral_high = 60

        self.logger.info("High-Accuracy Price Action Strategy initialized")
    
    def analyze_pair(self, df_15m: pd.DataFrame, df_5m: pd.DataFrame) -> Dict:
        """
        Main price action analysis function

        Args:
            df_15m: 15-minute timeframe data (trend analysis)
            df_5m: 5-minute timeframe data (entry signals)

        Returns:
            Dict containing analysis results and signals
        """
        try:
            # Step 1: Trend Analysis (M15)
            trend_analysis = self.analyze_trend(df_15m)

            # Step 2: Support/Resistance Levels (M15)
            levels_analysis = self.identify_key_levels(df_15m)

            # Step 3: Price Action Signals (M5)
            signal_analysis = self.find_price_action_signals(df_5m, trend_analysis, levels_analysis)

            return {
                'signal': signal_analysis.get('signal', 0),
                'entry_price': signal_analysis.get('entry_price'),
                'stop_loss': signal_analysis.get('stop_loss'),
                'take_profit': signal_analysis.get('take_profit'),
                'direction': signal_analysis.get('direction'),
                'confidence': signal_analysis.get('confidence', 0),
                'timeframe': 'M5',
                'bias': trend_analysis.get('bias', 'NEUTRAL'),
                'entry_zone': signal_analysis.get('entry_zone', 'Waiting for setup'),
                'pattern': signal_analysis.get('pattern', 'None')
            }

        except Exception as e:
            self.logger.error(f"Error in price action analysis: {e}")
            return {
                'signal': 0,
                'bias': 'NEUTRAL',
                'entry_zone': 'Analysis error',
                'confidence': 0
            }
    
    def analyze_trend(self, df: pd.DataFrame) -> Dict:
        """
        Simple trend analysis using moving averages and price action
        """
        try:
            if len(df) < 50:
                return {'bias': 'NEUTRAL', 'strength': 0}

            # Calculate moving averages
            df = df.copy()
            df['ema_20'] = df['close'].ewm(span=20).mean()
            df['ema_50'] = df['close'].ewm(span=50).mean()
            df['sma_200'] = df['close'].rolling(window=200).mean()
            df['rsi'] = self.calculate_rsi(df['close'], 14)

            current_price = df['close'].iloc[-1]
            ema_20 = df['ema_20'].iloc[-1]
            ema_50 = df['ema_50'].iloc[-1]
            sma_200 = df['sma_200'].iloc[-1]
            current_rsi = df['rsi'].iloc[-1]

            # Determine trend bias
            bias = 'NEUTRAL'
            strength = 0

            # BULLISH conditions
            if current_price > ema_20 > ema_50 > sma_200:
                bias = 'BULLISH'
                strength = 90
            elif current_price > ema_20 > ema_50:
                bias = 'BULLISH'
                strength = 75
            elif current_price > ema_20 and ema_20 > ema_50:
                bias = 'BULLISH'
                strength = 60

            # BEARISH conditions
            elif current_price < ema_20 < ema_50 < sma_200:
                bias = 'BEARISH'
                strength = 90
            elif current_price < ema_20 < ema_50:
                bias = 'BEARISH'
                strength = 75
            elif current_price < ema_20 and ema_20 < ema_50:
                bias = 'BEARISH'
                strength = 60

            return {
                'bias': bias,
                'strength': strength,
                'rsi': current_rsi,
                'ema_20': ema_20,
                'ema_50': ema_50,
                'sma_200': sma_200,
                'current_price': current_price
            }

        except Exception as e:
            self.logger.error(f"Error in trend analysis: {e}")
            return {'bias': 'NEUTRAL', 'strength': 0}
    
    def identify_key_levels(self, df: pd.DataFrame) -> Dict:
        """
        Identify key support and resistance levels
        """
        try:
            if len(df) < 20:
                return {'support_levels': [], 'resistance_levels': [], 'has_levels': False}

            support_levels = []
            resistance_levels = []

            # Look for swing highs and lows
            highs = df['high'].values
            lows = df['low'].values

            # Find local highs (resistance)
            for i in range(2, len(highs) - 2):
                if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and
                    highs[i] > highs[i+1] and highs[i] > highs[i+2]):

                    # Check if this level has been tested multiple times
                    level = highs[i]
                    touches = sum(1 for h in highs if abs(h - level) / level < 0.002)

                    if touches >= self.support_resistance_strength:
                        resistance_levels.append({
                            'level': level,
                            'touches': touches,
                            'strength': min(touches * 20, 100)
                        })

            # Find local lows (support)
            for i in range(2, len(lows) - 2):
                if (lows[i] < lows[i-1] and lows[i] < lows[i-2] and
                    lows[i] < lows[i+1] and lows[i] < lows[i+2]):

                    # Check if this level has been tested multiple times
                    level = lows[i]
                    touches = sum(1 for l in lows if abs(l - level) / level < 0.002)

                    if touches >= self.support_resistance_strength:
                        support_levels.append({
                            'level': level,
                            'touches': touches,
                            'strength': min(touches * 20, 100)
                        })

            # Sort by strength
            support_levels = sorted(support_levels, key=lambda x: x['strength'], reverse=True)[:3]
            resistance_levels = sorted(resistance_levels, key=lambda x: x['strength'], reverse=True)[:3]

            return {
                'support_levels': support_levels,
                'resistance_levels': resistance_levels,
                'has_levels': len(support_levels) > 0 or len(resistance_levels) > 0
            }

        except Exception as e:
            self.logger.error(f"Error in levels analysis: {e}")
            return {'support_levels': [], 'resistance_levels': [], 'has_levels': False}
    
    def find_price_action_signals(self, df: pd.DataFrame, trend: Dict, levels: Dict) -> Dict:
        """
        Find high-probability price action signals
        """
        try:
            if len(df) < 10:
                return {'signal': 0, 'confidence': 0}

            # Only proceed if trend has good strength
            if trend.get('strength', 0) < 75:
                return {'signal': 0, 'confidence': 0}

            # Get current data
            current_candle = df.iloc[-1]
            prev_candle = df.iloc[-2]
            prev2_candle = df.iloc[-3]
            current_price = current_candle['close']

            # Calculate indicators
            df_copy = df.copy()
            df_copy['rsi'] = self.calculate_rsi(df_copy['close'], 14)
            df_copy['volume_sma'] = df_copy['volume'].rolling(window=20).mean()

            current_rsi = df_copy['rsi'].iloc[-1]
            volume_ratio = current_candle['volume'] / df_copy['volume_sma'].iloc[-1]

            # Initialize signal variables
            signal = 0
            confidence = 0
            pattern = 'None'
            entry_price = current_price
            direction = 'NEUTRAL'

            # BULLISH SIGNALS
            if trend['bias'] == 'BULLISH':

                # 1. Bullish Engulfing Pattern
                if (prev_candle['close'] < prev_candle['open'] and  # Previous red
                    current_candle['close'] > current_candle['open'] and  # Current green
                    current_candle['close'] > prev_candle['open'] and  # Engulfs previous
                    current_candle['open'] < prev_candle['close'] and
                    volume_ratio > self.volume_multiplier and  # Volume confirmation
                    self.rsi_neutral_low < current_rsi < self.rsi_overbought):

                    signal = 1
                    confidence = 85
                    pattern = 'Bullish Engulfing'
                    direction = 'LONG'

                # 2. Bullish Hammer/Pin Bar
                elif self.is_bullish_hammer(current_candle, volume_ratio, current_rsi):
                    signal = 1
                    confidence = 80
                    pattern = 'Bullish Hammer'
                    direction = 'LONG'

                # 3. Support Level Bounce
                elif self.is_support_bounce(current_price, levels, current_candle, volume_ratio):
                    signal = 1
                    confidence = 75
                    pattern = 'Support Bounce'
                    direction = 'LONG'

                # 4. Moving Average Bounce
                elif (current_price > trend['ema_20'] and
                      prev_candle['low'] <= trend['ema_20'] and
                      current_candle['close'] > current_candle['open'] and
                      volume_ratio > 1.2 and
                      self.rsi_neutral_low < current_rsi < self.rsi_overbought):

                    signal = 1
                    confidence = 70
                    pattern = 'EMA Bounce'
                    direction = 'LONG'

            # BEARISH SIGNALS
            elif trend['bias'] == 'BEARISH':

                # 1. Bearish Engulfing Pattern
                if (prev_candle['close'] > prev_candle['open'] and  # Previous green
                    current_candle['close'] < current_candle['open'] and  # Current red
                    current_candle['close'] < prev_candle['open'] and  # Engulfs previous
                    current_candle['open'] > prev_candle['close'] and
                    volume_ratio > self.volume_multiplier and  # Volume confirmation
                    self.rsi_oversold < current_rsi < self.rsi_neutral_high):

                    signal = -1
                    confidence = 85
                    pattern = 'Bearish Engulfing'
                    direction = 'SHORT'

                # 2. Bearish Shooting Star
                elif self.is_bearish_shooting_star(current_candle, volume_ratio, current_rsi):
                    signal = -1
                    confidence = 80
                    pattern = 'Shooting Star'
                    direction = 'SHORT'

                # 3. Resistance Level Rejection
                elif self.is_resistance_rejection(current_price, levels, current_candle, volume_ratio):
                    signal = -1
                    confidence = 75
                    pattern = 'Resistance Rejection'
                    direction = 'SHORT'

                # 4. Moving Average Rejection
                elif (current_price < trend['ema_20'] and
                      prev_candle['high'] >= trend['ema_20'] and
                      current_candle['close'] < current_candle['open'] and
                      volume_ratio > 1.2 and
                      self.rsi_oversold < current_rsi < self.rsi_neutral_high):

                    signal = -1
                    confidence = 70
                    pattern = 'EMA Rejection'
                    direction = 'SHORT'

            # Generate trade parameters if signal found
            if signal != 0 and confidence >= self.min_confidence:
                stop_loss, take_profit = self.calculate_trade_levels(
                    entry_price, direction, df, trend, levels
                )

                return {
                    'signal': signal,
                    'entry_price': entry_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'direction': direction,
                    'confidence': confidence,
                    'pattern': pattern,
                    'entry_zone': f"{pattern} Setup"
                }

            return {'signal': 0, 'confidence': confidence}

        except Exception as e:
            self.logger.error(f"Error in price action signals: {e}")
            return {'signal': 0, 'confidence': 0}

    def is_bullish_hammer(self, candle: pd.Series, volume_ratio: float, rsi: float) -> bool:
        """Check if candle is a bullish hammer/pin bar"""
        try:
            body_size = abs(candle['close'] - candle['open'])
            total_size = candle['high'] - candle['low']
            lower_wick = min(candle['open'], candle['close']) - candle['low']
            upper_wick = candle['high'] - max(candle['open'], candle['close'])

            return (body_size > 0 and
                    total_size > body_size * 2 and  # Long wick relative to body
                    lower_wick > body_size * 1.5 and  # Long lower wick
                    upper_wick < body_size * 0.5 and  # Small upper wick
                    volume_ratio > 1.2 and  # Volume confirmation
                    self.rsi_neutral_low < rsi < self.rsi_overbought)
        except:
            return False

    def is_bearish_shooting_star(self, candle: pd.Series, volume_ratio: float, rsi: float) -> bool:
        """Check if candle is a bearish shooting star"""
        try:
            body_size = abs(candle['close'] - candle['open'])
            total_size = candle['high'] - candle['low']
            lower_wick = min(candle['open'], candle['close']) - candle['low']
            upper_wick = candle['high'] - max(candle['open'], candle['close'])

            return (body_size > 0 and
                    total_size > body_size * 2 and  # Long wick relative to body
                    upper_wick > body_size * 1.5 and  # Long upper wick
                    lower_wick < body_size * 0.5 and  # Small lower wick
                    volume_ratio > 1.2 and  # Volume confirmation
                    self.rsi_oversold < rsi < self.rsi_neutral_high)
        except:
            return False

    def is_support_bounce(self, price: float, levels: Dict, candle: pd.Series, volume_ratio: float) -> bool:
        """Check if price is bouncing from support level"""
        try:
            for support in levels.get('support_levels', []):
                level = support['level']
                distance = abs(price - level) / level

                if (distance < 0.005 and  # Close to support level
                    candle['low'] <= level * 1.002 and  # Touched support
                    candle['close'] > candle['open'] and  # Bullish candle
                    volume_ratio > 1.3):  # Strong volume
                    return True
            return False
        except:
            return False

    def is_resistance_rejection(self, price: float, levels: Dict, candle: pd.Series, volume_ratio: float) -> bool:
        """Check if price is rejecting from resistance level"""
        try:
            for resistance in levels.get('resistance_levels', []):
                level = resistance['level']
                distance = abs(price - level) / level

                if (distance < 0.005 and  # Close to resistance level
                    candle['high'] >= level * 0.998 and  # Touched resistance
                    candle['close'] < candle['open'] and  # Bearish candle
                    volume_ratio > 1.3):  # Strong volume
                    return True
            return False
        except:
            return False

    def calculate_trade_levels(self, entry_price: float, direction: str, df: pd.DataFrame,
                              trend: Dict, levels: Dict) -> Tuple[float, float]:
        """
        Calculate stop loss and take profit levels
        """
        try:
            # Calculate ATR for volatility-based levels
            atr = self.calculate_atr(df)

            if direction == 'LONG':
                # Stop loss below recent low or support
                stop_loss = entry_price - (atr * 2.0)

                # Check for nearby support levels
                for support in levels.get('support_levels', []):
                    level = support['level']
                    if level < entry_price and (entry_price - level) < (atr * 3.0):
                        stop_loss = level - (atr * 0.5)
                        break

                # Take profit based on risk-reward ratio
                sl_distance = entry_price - stop_loss
                take_profit = entry_price + (sl_distance * self.risk_reward_min)

                # Check for nearby resistance levels
                for resistance in levels.get('resistance_levels', []):
                    level = resistance['level']
                    if level > entry_price and level < take_profit:
                        take_profit = level - (atr * 0.2)  # Just before resistance
                        break

            else:  # SHORT
                # Stop loss above recent high or resistance
                stop_loss = entry_price + (atr * 2.0)

                # Check for nearby resistance levels
                for resistance in levels.get('resistance_levels', []):
                    level = resistance['level']
                    if level > entry_price and (level - entry_price) < (atr * 3.0):
                        stop_loss = level + (atr * 0.5)
                        break

                # Take profit based on risk-reward ratio
                sl_distance = stop_loss - entry_price
                take_profit = entry_price - (sl_distance * self.risk_reward_min)

                # Check for nearby support levels
                for support in levels.get('support_levels', []):
                    level = support['level']
                    if level < entry_price and level > take_profit:
                        take_profit = level + (atr * 0.2)  # Just above support
                        break

            return stop_loss, take_profit

        except Exception as e:
            self.logger.error(f"Error calculating trade levels: {e}")
            # Fallback to simple ATR-based levels
            atr = self.calculate_atr(df)
            if direction == 'LONG':
                return entry_price - (atr * 2), entry_price + (atr * 4)
            else:
                return entry_price + (atr * 2), entry_price - (atr * 4)

    def calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range for volatility-based SL"""
        try:
            if len(df) < period + 1:
                return df['high'].iloc[-1] - df['low'].iloc[-1]

            # Create a copy to avoid warnings
            df_copy = df.copy()

            high = df_copy['high']
            low = df_copy['low']
            close = df_copy['close'].shift(1)

            tr1 = high - low
            tr2 = abs(high - close)
            tr3 = abs(low - close)

            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = tr.rolling(window=period).mean().iloc[-1]

            return atr if not pd.isna(atr) else (df['high'].iloc[-1] - df['low'].iloc[-1])

        except Exception:
            return df['high'].iloc[-1] - df['low'].iloc[-1]

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except Exception:
            return pd.Series([50] * len(prices), index=prices.index)


