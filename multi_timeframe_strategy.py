"""
Order Block Multi-Timeframe Strategy
High-accuracy institutional trading strategy
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

class MultiTimeframeStrategy:
    """
    Order Block Multi-Timeframe Strategy

    Strategy:
    - Identify institutional order blocks on multiple timeframes
    - Confirm trend alignment across 5M/15M/30M
    - Enter on price rejection from order blocks with confluence
    - Use proper risk management with 1:2 R/R
    """

    def __init__(self):
        """Initialize the Order Block strategy"""
        self.logger = logging.getLogger(__name__)

        # Strategy parameters (optimized for 6-8 trades with 65%+ win rate)
        self.min_confidence = 87  # High but achievable threshold

        # Order Block parameters (optimized)
        self.scan_range_min = 30  # Minimum candles to scan
        self.scan_range_max = 70  # Good range for order blocks
        self.price_move_threshold = 2.8  # 2.8% price move for order blocks
        self.confirmation_candles = 4  # Good confirmation
        self.zone_buffer = 0.3  # Reasonable zones

        # Volume and RSI parameters (optimized)
        self.volume_multiplier = 1.4  # Reasonable volume confirmation
        self.rsi_oversold = 30
        self.rsi_overbought = 70

        # Risk management
        self.stop_loss_buffer = 0.5  # 0.5% beyond order block
        self.risk_reward_ratio = 2.0  # 1:2 R/R
        self.position_risk = 1.5  # 1.5% account risk

        # EMA for trend confirmation
        self.ema_period = 200

        self.logger.info("Order Block Strategy initialized")

    def analyze_pair(self, df_15m: pd.DataFrame, df_5m: pd.DataFrame) -> Dict:
        """Main analysis function for Order Block strategy"""
        try:
            # Create 30M data from 15M (combine every 2 candles)
            df_30m = self.create_30m_from_15m(df_15m)

            # Analyze multiple timeframes
            signal_result = self.detect_order_block_signals(df_5m, df_15m, df_30m)

            return {
                'signal': signal_result.get('signal', 0),
                'entry_price': signal_result.get('entry_price'),
                'stop_loss': signal_result.get('stop_loss'),
                'take_profit': signal_result.get('take_profit'),
                'direction': signal_result.get('direction'),
                'confidence': signal_result.get('confidence', 0),
                'timeframe': signal_result.get('timeframe', 'M5'),
                'bias': signal_result.get('bias', 'NEUTRAL'),
                'entry_zone': signal_result.get('entry_zone', 'Waiting'),
                'pattern': signal_result.get('pattern', 'None')
            }

        except Exception as e:
            self.logger.error(f"Error in Order Block analysis: {e}")
            return {
                'signal': 0,
                'bias': 'NEUTRAL',
                'entry_zone': 'Error',
                'confidence': 0
            }

    def create_30m_from_15m(self, df_15m: pd.DataFrame) -> pd.DataFrame:
        """Create 30M timeframe from 15M data"""
        try:
            if len(df_15m) < 2:
                return df_15m.copy()

            df_30m = []
            for i in range(0, len(df_15m) - 1, 2):
                candle1 = df_15m.iloc[i]
                candle2 = df_15m.iloc[i + 1] if i + 1 < len(df_15m) else candle1

                # Combine two 15M candles into one 30M candle
                combined = {
                    'open': candle1['open'],
                    'high': max(candle1['high'], candle2['high']),
                    'low': min(candle1['low'], candle2['low']),
                    'close': candle2['close'],
                    'volume': candle1['volume'] + candle2['volume']
                }
                df_30m.append(combined)

            return pd.DataFrame(df_30m)
        except:
            return df_15m.copy()

    def detect_order_block_signals(self, df_5m: pd.DataFrame, df_15m: pd.DataFrame, df_30m: pd.DataFrame) -> Dict:
        """Detect Order Block signals across multiple timeframes"""
        try:
            # Check minimum data requirements
            if len(df_5m) < 100 or len(df_15m) < 50 or len(df_30m) < 25:
                return {'signal': 0, 'confidence': 0}

            # Step 1: Check trend alignment across timeframes
            trends = self.check_trend_alignment(df_5m, df_15m, df_30m)
            if not trends['has_confluence']:
                return {'signal': 0, 'confidence': 0, 'bias': 'NEUTRAL'}

            # Step 2: Identify order blocks on each timeframe
            order_blocks_5m = self.identify_order_blocks(df_5m, 'M5')
            order_blocks_15m = self.identify_order_blocks(df_15m, 'M15')
            order_blocks_30m = self.identify_order_blocks(df_30m, 'M30')

            # Step 3: Check for price approaching order blocks
            current_price = df_5m.iloc[-1]['close']

            # Prioritize higher timeframes (30M > 15M > 5M)
            for timeframe, blocks, df in [('M30', order_blocks_30m, df_30m),
                                         ('M15', order_blocks_15m, df_15m),
                                         ('M5', order_blocks_5m, df_5m)]:

                signal_result = self.check_order_block_entry(
                    df, blocks, current_price, trends['bias'], timeframe
                )

                if signal_result['signal'] != 0:
                    return signal_result

            return {'signal': 0, 'confidence': 0, 'bias': trends['bias']}

        except Exception as e:
            self.logger.error(f"Error in Order Block detection: {e}")
            return {'signal': 0, 'confidence': 0}

    def check_trend_alignment(self, df_5m: pd.DataFrame, df_15m: pd.DataFrame, df_30m: pd.DataFrame) -> Dict:
        """Check trend alignment across multiple timeframes"""
        try:
            trends = {}

            # Check each timeframe
            for name, df in [('5m', df_5m), ('15m', df_15m), ('30m', df_30m)]:
                trend = self.check_single_timeframe_trend(df)
                trends[name] = trend

            # Count bullish and bearish trends
            bullish_count = sum(1 for trend in trends.values() if trend == 'BULLISH')
            bearish_count = sum(1 for trend in trends.values() if trend == 'BEARISH')

            # Balanced confluence requirement for better accuracy
            has_confluence = bullish_count >= 2 or bearish_count >= 2

            if bullish_count >= 2:
                bias = 'BULLISH'
            elif bearish_count >= 2:
                bias = 'BEARISH'
            else:
                bias = 'NEUTRAL'

            return {
                'has_confluence': has_confluence,
                'bias': bias,
                'trends': trends,
                'strength': max(bullish_count, bearish_count)
            }

        except Exception as e:
            self.logger.error(f"Error in trend alignment: {e}")
            return {'has_confluence': False, 'bias': 'NEUTRAL'}

    def check_single_timeframe_trend(self, df: pd.DataFrame) -> str:
        """Check trend for a single timeframe"""
        try:
            if len(df) < 20:
                return 'NEUTRAL'

            # Calculate EMA 200
            df = df.copy()
            df['ema_200'] = df['close'].ewm(span=self.ema_period).mean()

            current = df.iloc[-1]
            current_price = current['close']

            # Check price vs EMA 200
            above_ema = current_price > current['ema_200']

            # Check for higher highs and higher lows (bullish) or lower highs and lower lows (bearish)
            recent_data = df.tail(20)
            highs = recent_data['high'].values
            lows = recent_data['low'].values

            # Find swing points
            swing_highs = self.find_swing_points(highs, 'high')
            swing_lows = self.find_swing_points(lows, 'low')

            if len(swing_highs) >= 2 and len(swing_lows) >= 2:
                # Check for higher highs and higher lows
                higher_highs = swing_highs[-1] > swing_highs[-2]
                higher_lows = swing_lows[-1] > swing_lows[-2]

                # Check for lower highs and lower lows
                lower_highs = swing_highs[-1] < swing_highs[-2]
                lower_lows = swing_lows[-1] < swing_lows[-2]

                if above_ema and higher_highs and higher_lows:
                    return 'BULLISH'
                elif not above_ema and lower_highs and lower_lows:
                    return 'BEARISH'

            # Fallback to EMA trend
            if above_ema:
                return 'BULLISH'
            else:
                return 'BEARISH'

        except Exception as e:
            return 'NEUTRAL'

    def find_swing_points(self, data: np.ndarray, point_type: str) -> List[float]:
        """Find swing highs or lows in price data"""
        try:
            swings = []
            window = 3

            for i in range(window, len(data) - window):
                if point_type == 'high':
                    # Check if current point is higher than surrounding points
                    if all(data[i] >= data[j] for j in range(i - window, i + window + 1) if j != i):
                        swings.append(data[i])
                else:  # low
                    # Check if current point is lower than surrounding points
                    if all(data[i] <= data[j] for j in range(i - window, i + window + 1) if j != i):
                        swings.append(data[i])

            return swings[-5:] if len(swings) > 5 else swings  # Return last 5 swings

        except:
            return []

    def identify_order_blocks(self, df: pd.DataFrame, timeframe: str) -> List[Dict]:
        """Identify order blocks in the given timeframe"""
        try:
            if len(df) < self.scan_range_max:
                return []

            order_blocks = []
            scan_data = df.tail(self.scan_range_max)

            # Find swing lows for bullish order blocks
            lows = scan_data['low'].values
            highs = scan_data['high'].values
            closes = scan_data['close'].values

            for i in range(5, len(scan_data) - self.confirmation_candles):
                current_low = lows[i]
                current_high = highs[i]
                current_close = closes[i]

                # Check for bullish order block (swing low followed by strong move up)
                if self.is_swing_low(lows, i):
                    # Check if price rose significantly after this swing low
                    future_prices = closes[i:i + self.confirmation_candles]
                    if len(future_prices) > 0:
                        price_move = (max(future_prices) - current_low) / current_low * 100

                        if price_move >= self.price_move_threshold:
                            # Create bullish order block
                            zone_low = current_low * (1 - self.zone_buffer / 100)
                            zone_high = current_high

                            order_blocks.append({
                                'type': 'BULLISH',
                                'timeframe': timeframe,
                                'zone_low': zone_low,
                                'zone_high': zone_high,
                                'swing_price': current_low,
                                'strength': price_move,
                                'index': i
                            })

                # Check for bearish order block (swing high followed by strong move down)
                if self.is_swing_high(highs, i):
                    # Check if price fell significantly after this swing high
                    future_prices = closes[i:i + self.confirmation_candles]
                    if len(future_prices) > 0:
                        price_move = (current_high - min(future_prices)) / current_high * 100

                        if price_move >= self.price_move_threshold:
                            # Create bearish order block
                            zone_high = current_high * (1 + self.zone_buffer / 100)
                            zone_low = current_low

                            order_blocks.append({
                                'type': 'BEARISH',
                                'timeframe': timeframe,
                                'zone_low': zone_low,
                                'zone_high': zone_high,
                                'swing_price': current_high,
                                'strength': price_move,
                                'index': i
                            })

            # Sort by strength and return the strongest (top 3)
            order_blocks.sort(key=lambda x: x['strength'], reverse=True)
            # Only return order blocks with strength > 3.0%
            strong_blocks = [block for block in order_blocks if block['strength'] > 3.0]
            return strong_blocks[:3]

        except Exception as e:
            self.logger.error(f"Error identifying order blocks: {e}")
            return []

    def is_swing_low(self, lows: np.ndarray, index: int, window: int = 3) -> bool:
        """Check if the given index is a swing low"""
        try:
            if index < window or index >= len(lows) - window:
                return False

            current_low = lows[index]

            # Check if current low is lower than surrounding lows
            for i in range(index - window, index + window + 1):
                if i != index and lows[i] <= current_low:
                    return False

            return True
        except:
            return False

    def is_swing_high(self, highs: np.ndarray, index: int, window: int = 3) -> bool:
        """Check if the given index is a swing high"""
        try:
            if index < window or index >= len(highs) - window:
                return False

            current_high = highs[index]

            # Check if current high is higher than surrounding highs
            for i in range(index - window, index + window + 1):
                if i != index and highs[i] >= current_high:
                    return False

            return True
        except:
            return False

    def check_order_block_entry(self, df: pd.DataFrame, order_blocks: List[Dict],
                               current_price: float, trend_bias: str, timeframe: str) -> Dict:
        """Check for entry signals at order blocks"""
        try:
            if not order_blocks:
                return {'signal': 0, 'confidence': 0}

            # Calculate indicators
            df = df.copy()
            df['rsi'] = self.calculate_rsi(df['close'])
            df['volume_avg'] = df['volume'].rolling(20).mean()

            current_candle = df.iloc[-1]
            prev_candle = df.iloc[-2]
            current_rsi = current_candle['rsi']
            volume_ratio = current_candle['volume'] / current_candle['volume_avg']

            # Check each order block
            for block in order_blocks:
                # Skip if block type doesn't match trend bias
                if block['type'] != trend_bias:
                    continue

                # Check if price is approaching the order block
                in_zone = (block['zone_low'] <= current_price <= block['zone_high'])
                approaching_zone = self.is_approaching_zone(current_price, block, trend_bias)

                if in_zone or approaching_zone:
                    # Check for confirmation patterns
                    confirmation = self.check_order_block_confirmation(
                        current_candle, prev_candle, current_rsi, volume_ratio, trend_bias
                    )

                    if confirmation['has_confirmation']:
                        # Calculate entry, SL, and TP
                        entry_price = current_price
                        stop_loss, take_profit = self.calculate_order_block_levels(
                            entry_price, block, trend_bias
                        )

                        # Calculate confidence based on multiple factors
                        confidence = self.calculate_confidence(
                            block, confirmation, timeframe, volume_ratio
                        )

                        if confidence >= self.min_confidence:
                            signal = 1 if trend_bias == 'BULLISH' else -1
                            direction = 'LONG' if trend_bias == 'BULLISH' else 'SHORT'

                            return {
                                'signal': signal,
                                'entry_price': entry_price,
                                'stop_loss': stop_loss,
                                'take_profit': take_profit,
                                'direction': direction,
                                'confidence': confidence,
                                'timeframe': timeframe,
                                'pattern': f"Order Block {trend_bias.title()}",
                                'bias': trend_bias,
                                'entry_zone': f"{timeframe} Order Block"
                            }

            return {'signal': 0, 'confidence': 0}

        except Exception as e:
            self.logger.error(f"Error checking order block entry: {e}")
            return {'signal': 0, 'confidence': 0}

    def is_approaching_zone(self, current_price: float, block: Dict, trend_bias: str) -> bool:
        """Check if price is approaching an order block zone"""
        try:
            zone_center = (block['zone_low'] + block['zone_high']) / 2
            distance_pct = abs(current_price - zone_center) / zone_center * 100

            # Consider "approaching" if within 1.5% of the zone (slightly relaxed)
            return distance_pct <= 1.5
        except:
            return False

    def check_order_block_confirmation(self, current: pd.Series, prev: pd.Series,
                                     rsi: float, volume_ratio: float, trend_bias: str) -> Dict:
        """Check for confirmation patterns at order blocks"""
        try:
            confirmations = []

            # 1. Pin bar / Engulfing pattern
            if trend_bias == 'BULLISH':
                # Bullish pin bar or engulfing
                is_pinbar = self.is_bullish_pinbar(current)
                is_engulfing = self.is_bullish_engulfing(current, prev)

                if is_pinbar or is_engulfing:
                    confirmations.append('pattern')

                # RSI not oversold
                if rsi > self.rsi_oversold:
                    confirmations.append('rsi')

            else:  # BEARISH
                # Bearish pin bar or engulfing
                is_pinbar = self.is_bearish_pinbar(current)
                is_engulfing = self.is_bearish_engulfing(current, prev)

                if is_pinbar or is_engulfing:
                    confirmations.append('pattern')

                # RSI not overbought
                if rsi < self.rsi_overbought:
                    confirmations.append('rsi')

            # 2. Volume confirmation
            if volume_ratio > self.volume_multiplier:
                confirmations.append('volume')

            # Need at least 2 confirmations (or 1 strong confirmation)
            has_confirmation = len(confirmations) >= 2 or ('pattern' in confirmations and 'volume' in confirmations)

            return {
                'has_confirmation': has_confirmation,
                'confirmations': confirmations,
                'count': len(confirmations)
            }

        except:
            return {'has_confirmation': False, 'confirmations': [], 'count': 0}

    def calculate_order_block_levels(self, entry_price: float, block: Dict, trend_bias: str) -> Tuple[float, float]:
        """Calculate stop loss and take profit for order block trades"""
        try:
            if trend_bias == 'BULLISH':
                # Long trade: SL below order block, TP based on R/R
                stop_loss = block['zone_low'] * (1 - self.stop_loss_buffer / 100)
                risk = entry_price - stop_loss
                take_profit = entry_price + (risk * self.risk_reward_ratio)
            else:
                # Short trade: SL above order block, TP based on R/R
                stop_loss = block['zone_high'] * (1 + self.stop_loss_buffer / 100)
                risk = stop_loss - entry_price
                take_profit = entry_price - (risk * self.risk_reward_ratio)

            return stop_loss, take_profit

        except:
            # Fallback to percentage-based levels
            if trend_bias == 'BULLISH':
                return entry_price * 0.995, entry_price * 1.02
            else:
                return entry_price * 1.005, entry_price * 0.98

    def calculate_confidence(self, block: Dict, confirmation: Dict, timeframe: str, volume_ratio: float) -> int:
        """Calculate confidence score for the trade"""
        try:
            confidence = 75  # Higher base confidence

            # Order block strength (more selective)
            if block['strength'] > 4.0:
                confidence += 20
            elif block['strength'] > 3.5:
                confidence += 15
            elif block['strength'] > 3.0:
                confidence += 10
            elif block['strength'] > 2.5:
                confidence += 5
            else:
                confidence -= 5  # Penalize weak order blocks

            # Confirmation count (higher weight)
            confidence += confirmation['count'] * 7

            # Timeframe priority (higher timeframes get more confidence)
            if timeframe == 'M30':
                confidence += 20
            elif timeframe == 'M15':
                confidence += 12
            elif timeframe == 'M5':
                confidence += 5

            # Volume confirmation (more selective)
            if volume_ratio > 2.5:
                confidence += 15
            elif volume_ratio > 2.0:
                confidence += 10
            elif volume_ratio > 1.5:
                confidence += 5
            else:
                confidence -= 5  # Penalize low volume

            return min(confidence, 100)  # Cap at 100

        except:
            return 75

    def is_bullish_pinbar(self, candle: pd.Series) -> bool:
        """Check for bullish pin bar pattern"""
        try:
            body_size = abs(candle['close'] - candle['open'])
            total_size = candle['high'] - candle['low']
            lower_wick = min(candle['open'], candle['close']) - candle['low']

            return (total_size > 0 and
                    lower_wick > body_size * 2 and
                    body_size / total_size < 0.3)
        except:
            return False

    def is_bearish_pinbar(self, candle: pd.Series) -> bool:
        """Check for bearish pin bar pattern"""
        try:
            body_size = abs(candle['close'] - candle['open'])
            total_size = candle['high'] - candle['low']
            upper_wick = candle['high'] - max(candle['open'], candle['close'])

            return (total_size > 0 and
                    upper_wick > body_size * 2 and
                    body_size / total_size < 0.3)
        except:
            return False

    def is_bullish_engulfing(self, current: pd.Series, prev: pd.Series) -> bool:
        """Check for bullish engulfing pattern"""
        try:
            return (prev['close'] < prev['open'] and  # Previous bearish
                    current['close'] > current['open'] and  # Current bullish
                    current['close'] > prev['open'] and  # Engulfs previous
                    current['open'] < prev['close'])
        except:
            return False

    def is_bearish_engulfing(self, current: pd.Series, prev: pd.Series) -> bool:
        """Check for bearish engulfing pattern"""
        try:
            return (prev['close'] > prev['open'] and  # Previous bullish
                    current['close'] < current['open'] and  # Current bearish
                    current['close'] < prev['open'] and  # Engulfs previous
                    current['open'] > prev['close'])
        except:
            return False

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series([50] * len(prices), index=prices.index)
