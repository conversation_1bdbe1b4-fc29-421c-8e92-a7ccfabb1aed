"""
Simple High-Accuracy Trading Strategy
Focus: 65%+ win rate with proven patterns
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

class MultiTimeframeStrategy:
    """
    Simple High-Accuracy Strategy for 65%+ Win Rate
    
    Focus on:
    1. Strong trend confirmation
    2. Simple but effective patterns
    3. Conservative entry criteria
    4. Good risk management
    """

    def __init__(self):
        """Initialize the simple strategy"""
        self.logger = logging.getLogger(__name__)

        # Ultra-conservative parameters for 65%+ win rate
        self.min_confidence = 85  # Very high threshold
        self.risk_reward_min = 1.5  # More flexible RR for higher win rate
        self.risk_reward_max = 3.0  # Conservative max

        # Strict indicators
        self.rsi_oversold = 25
        self.rsi_overbought = 75
        self.volume_threshold = 1.8  # Strong volume confirmation
        self.trend_strength_min = 0.7  # Strong trend required

        self.logger.info("Simple High-Accuracy Strategy initialized")

    def analyze_pair(self, df_15m: pd.DataFrame, df_5m: pd.DataFrame) -> Dict:
        """Main analysis function"""
        try:
            # Use 5M for signals, 15M for trend confirmation
            signal_result = self.find_high_accuracy_signals(df_5m, df_15m)
            
            return {
                'signal': signal_result.get('signal', 0),
                'entry_price': signal_result.get('entry_price'),
                'stop_loss': signal_result.get('stop_loss'),
                'take_profit': signal_result.get('take_profit'),
                'direction': signal_result.get('direction'),
                'confidence': signal_result.get('confidence', 0),
                'timeframe': 'M5',
                'bias': signal_result.get('bias', 'NEUTRAL'),
                'entry_zone': signal_result.get('entry_zone', 'Waiting'),
                'pattern': signal_result.get('pattern', 'None')
            }
            
        except Exception as e:
            self.logger.error(f"Error in analysis: {e}")
            return {
                'signal': 0,
                'bias': 'NEUTRAL',
                'entry_zone': 'Error',
                'confidence': 0
            }

    def find_high_accuracy_signals(self, df_5m: pd.DataFrame, df_15m: pd.DataFrame) -> Dict:
        """Find high-accuracy trading signals"""
        try:
            if len(df_5m) < 50 or len(df_15m) < 50:
                return {'signal': 0, 'confidence': 0}

            # Calculate indicators
            df_5m = df_5m.copy()
            df_15m = df_15m.copy()
            
            # 5M indicators
            df_5m['ema_20'] = df_5m['close'].ewm(span=20).mean()
            df_5m['ema_50'] = df_5m['close'].ewm(span=50).mean()
            df_5m['rsi'] = self.calculate_rsi(df_5m['close'])
            df_5m['volume_avg'] = df_5m['volume'].rolling(20).mean()
            
            # 15M indicators for trend
            df_15m['ema_20'] = df_15m['close'].ewm(span=20).mean()
            df_15m['ema_50'] = df_15m['close'].ewm(span=50).mean()
            df_15m['rsi'] = self.calculate_rsi(df_15m['close'])

            # Current data
            current_5m = df_5m.iloc[-1]
            prev_5m = df_5m.iloc[-2]
            current_15m = df_15m.iloc[-1]
            
            current_price = current_5m['close']
            current_rsi = current_5m['rsi']
            volume_ratio = current_5m['volume'] / current_5m['volume_avg']

            # Trend analysis (15M)
            trend_bias = self.get_trend_bias(df_15m)
            if trend_bias == 'NEUTRAL':
                return {'signal': 0, 'confidence': 0}

            # Signal detection
            signal = 0
            confidence = 0
            pattern = 'None'
            direction = 'NEUTRAL'

            # ULTRA-STRICT BULLISH SIGNALS
            if trend_bias == 'BULLISH':

                # 1. Perfect Bullish Engulfing with Multiple Confirmations
                if (prev_5m['close'] < prev_5m['open'] and  # Previous red
                    current_5m['close'] > current_5m['open'] and  # Current green
                    current_5m['close'] > prev_5m['open'] and  # Full engulf
                    current_5m['open'] < prev_5m['close'] and
                    abs(current_5m['close'] - current_5m['open']) > abs(prev_5m['close'] - prev_5m['open']) * 1.5 and  # Much larger body
                    volume_ratio > self.volume_threshold and
                    current_price > current_5m['ema_20'] and  # Above EMA
                    current_15m['rsi'] > 50 and  # 15M RSI bullish
                    40 < current_rsi < 65):  # 5M RSI healthy

                    signal = 1
                    confidence = 95
                    pattern = 'Perfect Bullish Engulfing'
                    direction = 'LONG'

                # 2. Strong EMA Bounce with Confirmation
                elif (current_price > current_5m['ema_20'] and
                      prev_5m['low'] <= current_5m['ema_20'] * 1.002 and  # Touched EMA
                      current_5m['close'] > current_5m['open'] and
                      current_5m['close'] > prev_5m['high'] and  # Break previous high
                      volume_ratio > self.volume_threshold and
                      current_15m['close'] > current_15m['ema_20'] and  # 15M above EMA
                      45 < current_rsi < 60):  # Healthy RSI

                    signal = 1
                    confidence = 90
                    pattern = 'Strong EMA Bounce'
                    direction = 'LONG'

            # ULTRA-STRICT BEARISH SIGNALS
            elif trend_bias == 'BEARISH':

                # 1. Perfect Bearish Engulfing with Multiple Confirmations
                if (prev_5m['close'] > prev_5m['open'] and  # Previous green
                    current_5m['close'] < current_5m['open'] and  # Current red
                    current_5m['close'] < prev_5m['open'] and  # Full engulf
                    current_5m['open'] > prev_5m['close'] and
                    abs(current_5m['close'] - current_5m['open']) > abs(prev_5m['close'] - prev_5m['open']) * 1.5 and  # Much larger body
                    volume_ratio > self.volume_threshold and
                    current_price < current_5m['ema_20'] and  # Below EMA
                    current_15m['rsi'] < 50 and  # 15M RSI bearish
                    35 < current_rsi < 60):  # 5M RSI healthy

                    signal = -1
                    confidence = 95
                    pattern = 'Perfect Bearish Engulfing'
                    direction = 'SHORT'

                # 2. Strong EMA Rejection with Confirmation
                elif (current_price < current_5m['ema_20'] and
                      prev_5m['high'] >= current_5m['ema_20'] * 0.998 and  # Touched EMA
                      current_5m['close'] < current_5m['open'] and
                      current_5m['close'] < prev_5m['low'] and  # Break previous low
                      volume_ratio > self.volume_threshold and
                      current_15m['close'] < current_15m['ema_20'] and  # 15M below EMA
                      40 < current_rsi < 55):  # Healthy RSI

                    signal = -1
                    confidence = 90
                    pattern = 'Strong EMA Rejection'
                    direction = 'SHORT'

            # Generate trade if signal found
            if signal != 0 and confidence >= self.min_confidence:
                stop_loss, take_profit = self.calculate_levels(
                    current_price, direction, df_5m
                )
                
                return {
                    'signal': signal,
                    'entry_price': current_price,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'direction': direction,
                    'confidence': confidence,
                    'pattern': pattern,
                    'bias': trend_bias,
                    'entry_zone': f"{pattern} Setup"
                }

            return {'signal': 0, 'confidence': confidence, 'bias': trend_bias}

        except Exception as e:
            self.logger.error(f"Error in signal detection: {e}")
            return {'signal': 0, 'confidence': 0}

    def get_trend_bias(self, df: pd.DataFrame) -> str:
        """Ultra-strict trend detection for high accuracy"""
        try:
            current = df.iloc[-1]
            prev = df.iloc[-2]

            # Calculate trend strength
            trend_strength = self.calculate_trend_strength(df)
            if trend_strength < self.trend_strength_min:
                return 'NEUTRAL'

            # Very strict EMA alignment with momentum
            if (current['close'] > current['ema_20'] > current['ema_50'] and
                current['ema_20'] > prev['ema_20'] and  # EMA rising
                current['rsi'] > 50 and current['rsi'] < 70 and  # Healthy momentum
                current['close'] > prev['close']):  # Price rising
                return 'BULLISH'
            elif (current['close'] < current['ema_20'] < current['ema_50'] and
                  current['ema_20'] < prev['ema_20'] and  # EMA falling
                  current['rsi'] < 50 and current['rsi'] > 30 and  # Healthy momentum
                  current['close'] < prev['close']):  # Price falling
                return 'BEARISH'
            else:
                return 'NEUTRAL'
        except:
            return 'NEUTRAL'

    def calculate_trend_strength(self, df: pd.DataFrame) -> float:
        """Calculate trend strength (0-1)"""
        try:
            if len(df) < 10:
                return 0

            closes = df['close'].tail(10).values
            trend_score = 0

            # Count consecutive moves in same direction
            for i in range(1, len(closes)):
                if closes[i] > closes[i-1]:
                    trend_score += 1
                elif closes[i] < closes[i-1]:
                    trend_score -= 1

            return abs(trend_score) / (len(closes) - 1)
        except:
            return 0

    def calculate_levels(self, entry_price: float, direction: str, df: pd.DataFrame) -> Tuple[float, float]:
        """Calculate stop loss and take profit"""
        try:
            atr = self.calculate_atr(df)
            
            if direction == 'LONG':
                stop_loss = entry_price - (atr * 2.0)
                take_profit = entry_price + (atr * 4.0)  # 1:2 RR
            else:
                stop_loss = entry_price + (atr * 2.0)
                take_profit = entry_price - (atr * 4.0)  # 1:2 RR
                
            return stop_loss, take_profit
        except:
            # Fallback
            if direction == 'LONG':
                return entry_price * 0.99, entry_price * 1.02
            else:
                return entry_price * 1.01, entry_price * 0.98

    def calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate ATR"""
        try:
            high = df['high']
            low = df['low']
            close = df['close'].shift(1)
            
            tr1 = high - low
            tr2 = abs(high - close)
            tr3 = abs(low - close)
            
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = tr.rolling(window=period).mean().iloc[-1]
            
            return atr if not pd.isna(atr) else (df['high'].iloc[-1] - df['low'].iloc[-1])
        except:
            return df['high'].iloc[-1] - df['low'].iloc[-1]

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series([50] * len(prices), index=prices.index)
