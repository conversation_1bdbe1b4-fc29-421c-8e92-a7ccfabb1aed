"""
Enhanced High-Quality SMC Strategy
Advanced Smart Money Concepts with strict quality filters
Target: High win rate with fewer, better trades
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

class MultiTimeframeStrategy:
    """
    Enhanced High-Quality SMC Strategy

    Strategy Overview:
    1. Strict Quality Filters: Only high-probability setups
    2. Advanced Confluence: Multiple confirmations required
    3. Dynamic Risk Management: Adaptive SL/TP based on volatility
    4. Target: 70%+ win rate with 1:3+ RR ratio
    """

    def __init__(self):
        """Initialize the enhanced strategy"""
        self.logger = logging.getLogger(__name__)

        # Balanced strategy parameters for optimal performance
        self.min_confidence = 48  # Balanced threshold for quality vs quantity
        self.target_pips = 15  # Increased target for better RR
        self.risk_reward_min = 1.6  # Balanced minimum RR
        self.risk_reward_max = 4.0  # Keep maximum RR
        self.max_trades_per_session = 7  # Allow more quality trades

        # Balanced adaptive parameters
        self.volatility_multiplier = 1.15  # Balanced SL/TP
        self.trend_strength_threshold = 0.6  # Balanced threshold for trend trades
        self.momentum_threshold = 0.7  # Balanced threshold for momentum trades

        self.logger.info("Enhanced Adaptive SMC Strategy initialized")
    
    def analyze_pair(self, df_15m: pd.DataFrame, df_5m: pd.DataFrame) -> Dict:
        """
        Main SMC analysis function
        
        Args:
            df_15m: 15-minute timeframe data (structure analysis)
            df_5m: 5-minute timeframe data (entry confirmation)
            
        Returns:
            Dict containing analysis results and signals
        """
        try:
            # Step 1: Structure Analysis (M15)
            structure_analysis = self.analyze_market_structure(df_15m)
            
            # Step 2: Smart Zones Identification (M15)
            zones_analysis = self.identify_smart_zones(df_15m)
            
            # Step 3: Entry Confirmation (M5)
            entry_analysis = self.confirm_entry(df_5m, structure_analysis, zones_analysis)
            
            # Step 4: Generate Signal
            signal = self.generate_smc_signal(structure_analysis, zones_analysis, entry_analysis, df_5m)
            
            return {
                'structure_analysis': structure_analysis,
                'zones_analysis': zones_analysis,
                'entry_analysis': entry_analysis,
                'signal': signal.get('signal', 0),
                'entry_price': signal.get('entry_price'),
                'stop_loss': signal.get('stop_loss'),
                'take_profit': signal.get('take_profit'),
                'direction': signal.get('direction'),
                'confidence': signal.get('confidence', 0),
                'timeframe': signal.get('timeframe', 'M5'),
                'bias': structure_analysis.get('bias', 'NEUTRAL'),
                'entry_zone': signal.get('entry_zone', 'Waiting for setup')
            }
            
        except Exception as e:
            self.logger.error(f"Error in SMC analysis: {e}")
            return {
                'signal': 0,
                'bias': 'NEUTRAL',
                'entry_zone': 'Analysis error',
                'confidence': 0
            }
    
    def analyze_market_structure(self, df: pd.DataFrame) -> Dict:
        """
        Enhanced Structure Analysis with strict quality filters
        Only identifies clear, high-probability market structures
        """
        try:
            if len(df) < 30:  # Reduced data requirement
                return {'bias': 'NEUTRAL', 'bos_choch': False, 'structure_strength': 0}

            # Create a copy to avoid SettingWithCopyWarning
            df_copy = df.copy()

            # Advanced trend analysis with multiple timeframes
            df_copy['ema_21'] = df_copy['close'].ewm(span=21).mean()
            df_copy['ema_50'] = df_copy['close'].ewm(span=50).mean()
            df_copy['ema_200'] = df_copy['close'].ewm(span=200).mean()

            # Calculate momentum indicators
            df_copy['rsi'] = self.calculate_rsi(df_copy['close'], 14)
            df_copy['macd'] = self.calculate_macd(df_copy['close'])

            current_price = df_copy['close'].iloc[-1]
            ema_21 = df_copy['ema_21'].iloc[-1]
            ema_50 = df_copy['ema_50'].iloc[-1]
            ema_200 = df_copy['ema_200'].iloc[-1]
            current_rsi = df_copy['rsi'].iloc[-1]
            current_macd = df_copy['macd'].iloc[-1]

            # Identify swing points with higher precision
            swing_highs, swing_lows = self.identify_swing_points(df_copy)

            # Determine bias with strict criteria
            bias = 'NEUTRAL'
            structure_strength = 0
            bos_choch = False

            # STRONG BULLISH: Perfect alignment + momentum
            if (current_price > ema_21 > ema_50 > ema_200 and  # Perfect EMA alignment
                current_rsi > 50 and current_rsi < 80 and  # Healthy momentum
                current_macd > 0 and  # Positive MACD
                len(swing_highs) >= 2 and len(swing_lows) >= 2 and
                swing_highs[-1] > swing_highs[-2] and  # Higher High
                swing_lows[-1] > swing_lows[-2]):  # Higher Low

                bias = 'BULLISH'
                structure_strength = 85
                bos_choch = True

            # GOOD BULLISH: Strong EMA alignment
            elif (current_price > ema_21 > ema_50 and  # Good EMA alignment
                  current_rsi > 45 and current_rsi < 75 and  # Decent momentum
                  current_macd > -0.001):  # MACD not strongly negative

                bias = 'BULLISH'
                structure_strength = 75
                bos_choch = False

            # MODERATE BULLISH: Basic bullish structure
            elif (current_price > ema_21 and  # Price above short EMA
                  current_rsi > 40 and current_rsi < 80 and  # Reasonable momentum
                  ema_21 > ema_50):  # Short EMA above medium EMA

                bias = 'BULLISH'
                structure_strength = 65
                bos_choch = False

            # STRONG BEARISH: Perfect alignment + momentum
            elif (current_price < ema_21 < ema_50 < ema_200 and  # Perfect EMA alignment
                  current_rsi < 50 and current_rsi > 20 and  # Healthy momentum
                  current_macd < 0 and  # Negative MACD
                  len(swing_highs) >= 2 and len(swing_lows) >= 2 and
                  swing_highs[-1] < swing_highs[-2] and  # Lower High
                  swing_lows[-1] < swing_lows[-2]):  # Lower Low

                bias = 'BEARISH'
                structure_strength = 85
                bos_choch = True

            # GOOD BEARISH: Strong EMA alignment
            elif (current_price < ema_21 < ema_50 and  # Good EMA alignment
                  current_rsi < 55 and current_rsi > 25 and  # Decent momentum
                  current_macd < 0.001):  # MACD not strongly positive

                bias = 'BEARISH'
                structure_strength = 75
                bos_choch = False

            # MODERATE BEARISH: Basic bearish structure
            elif (current_price < ema_21 and  # Price below short EMA
                  current_rsi < 60 and current_rsi > 20 and  # Reasonable momentum
                  ema_21 < ema_50):  # Short EMA below medium EMA

                bias = 'BEARISH'
                structure_strength = 65
                bos_choch = False

            # Market condition filter - avoid very choppy markets
            market_volatility = self.calculate_market_volatility(df_copy)
            if market_volatility < 0.25:  # Very choppy/sideways
                bias = 'NEUTRAL'
                structure_strength = 0
                bos_choch = False
            elif structure_strength < 50:  # Balanced threshold
                bias = 'NEUTRAL'
                structure_strength = 0
                bos_choch = False

            return {
                'bias': bias,
                'bos_choch': bos_choch,
                'structure_strength': structure_strength,
                'swing_highs': swing_highs,
                'swing_lows': swing_lows,
                'rsi': current_rsi,
                'macd': current_macd,
                'ema_alignment': self.check_ema_alignment(ema_21, ema_50, ema_200)
            }

        except Exception as e:
            self.logger.error(f"Error in enhanced structure analysis: {e}")
            return {'bias': 'NEUTRAL', 'bos_choch': False, 'structure_strength': 0}
    
    def identify_smart_zones(self, df: pd.DataFrame) -> Dict:
        """
        Step 2: Identify Smart Zones (M15)
        Mark Order Blocks and liquidity zones
        """
        try:
            if len(df) < 10:
                return {'order_blocks': [], 'liquidity_zones': [], 'has_zones': False}
            
            order_blocks = []
            liquidity_zones = []
            
            # Identify Order Blocks (strong rejection candles)
            for i in range(5, len(df) - 1):
                current = df.iloc[i]
                
                body_size = abs(current['close'] - current['open'])
                wick_size = current['high'] - current['low']
                
                # Bullish Order Block (hammer-like with strong rejection)
                if (current['close'] > current['open'] and 
                    body_size > 0 and wick_size > body_size * 2):
                    
                    order_blocks.append({
                        'type': 'bullish_ob',
                        'level': current['low'],
                        'strength': min((wick_size / body_size) * 20, 100),
                        'index': i
                    })
                
                # Bearish Order Block (shooting star-like with strong rejection)
                elif (current['close'] < current['open'] and 
                      body_size > 0 and wick_size > body_size * 2):
                    
                    order_blocks.append({
                        'type': 'bearish_ob',
                        'level': current['high'],
                        'strength': min((wick_size / body_size) * 20, 100),
                        'index': i
                    })
            
            # Identify support/resistance levels as liquidity zones
            highs = df['high'].tail(20).values
            lows = df['low'].tail(20).values
            
            # Find recent support/resistance
            resistance_level = np.percentile(highs, 90)
            support_level = np.percentile(lows, 10)
            
            liquidity_zones.append({
                'type': 'resistance',
                'level': resistance_level,
                'strength': 70
            })
            
            liquidity_zones.append({
                'type': 'support',
                'level': support_level,
                'strength': 70
            })
            
            # Keep only strong zones
            order_blocks = [ob for ob in order_blocks if ob['strength'] > 30]
            
            return {
                'order_blocks': order_blocks,
                'liquidity_zones': liquidity_zones,
                'has_zones': len(order_blocks) > 0 or len(liquidity_zones) > 0
            }
            
        except Exception as e:
            self.logger.error(f"Error in zones analysis: {e}")
            return {'order_blocks': [], 'liquidity_zones': [], 'has_zones': False}
    
    def confirm_entry(self, df: pd.DataFrame, structure: Dict, zones: Dict) -> Dict:
        """
        Enhanced Entry Confirmation with strict quality filters
        Only accepts perfect setups with multiple confirmations
        """
        try:
            if len(df) < 10:  # Reduced data requirement
                return {'has_entry': False, 'entry_type': None, 'strength': 0}

            # Only proceed if structure has some strength
            if structure.get('structure_strength', 0) < 50:
                return {'has_entry': False, 'entry_type': None, 'strength': 0}

            current_candle = df.iloc[-1]
            prev_candle = df.iloc[-2]
            prev2_candle = df.iloc[-3]
            current_price = current_candle['close']

            # Calculate additional indicators for confirmation
            df_copy = df.copy()
            df_copy['rsi'] = self.calculate_rsi(df_copy['close'], 14)
            df_copy['volume_sma'] = df_copy['volume'].rolling(window=20).mean()

            current_rsi = df_copy['rsi'].iloc[-1]
            volume_ratio = current_candle['volume'] / df_copy['volume_sma'].iloc[-1]

            entry_signals = []

            # BULLISH SETUPS
            if structure['bias'] == 'BULLISH':
                # 1. Strong Engulfing (more flexible)
                if (prev_candle['close'] < prev_candle['open'] and  # Previous red
                    current_candle['close'] > current_candle['open'] and  # Current green
                    current_candle['close'] > prev_candle['open'] and  # Full engulf
                    current_candle['open'] < prev_candle['close'] and
                    volume_ratio > 1.1 and  # More flexible volume requirement
                    current_rsi > 35 and current_rsi < 85):  # More flexible RSI

                    entry_signals.append({
                        'type': 'strong_bullish_engulfing',
                        'strength': 35
                    })

                # 2. Bullish Pin Bar/Hammer (more flexible)
                body_size = abs(current_candle['close'] - current_candle['open'])
                total_size = current_candle['high'] - current_candle['low']
                lower_wick = current_candle['close'] - current_candle['low']

                if (body_size > 0 and total_size > body_size * 1.5 and  # More flexible rejection
                    lower_wick > body_size * 1.2 and  # More flexible wick requirement
                    current_candle['close'] > current_candle['open'] and  # Green candle
                    volume_ratio > 0.8 and  # More flexible volume
                    current_rsi > 30 and current_rsi < 80):  # More flexible RSI

                    entry_signals.append({
                        'type': 'bullish_hammer',
                        'strength': 30
                    })

                # 3. Bullish Momentum Breakout
                if (current_candle['close'] > current_candle['open'] and  # Green candle
                    current_candle['close'] > prev_candle['high'] and  # Break previous high
                    current_rsi > 45 and current_rsi < 75):  # Good momentum

                    entry_signals.append({
                        'type': 'bullish_momentum',
                        'strength': 28
                    })

                # 4. Bullish Trend Continuation
                if (current_candle['close'] > current_candle['open'] and  # Green candle
                    current_candle['low'] > prev_candle['low'] and  # Higher low
                    current_rsi > 40 and current_rsi < 70 and  # Healthy RSI
                    volume_ratio > 0.9):  # Decent volume

                    entry_signals.append({
                        'type': 'bullish_continuation',
                        'strength': 25
                    })

                # 5. Three White Soldiers Pattern
                if (len(df) >= 3 and
                    current_candle['close'] > current_candle['open'] and
                    prev_candle['close'] > prev_candle['open'] and
                    prev2_candle['close'] > prev2_candle['open'] and
                    current_candle['close'] > prev_candle['close'] > prev2_candle['close']):

                    entry_signals.append({
                        'type': 'three_white_soldiers',
                        'strength': 32
                    })

            # BEARISH SETUPS
            elif structure['bias'] == 'BEARISH':
                # 1. Strong Bearish Engulfing (more flexible)
                if (prev_candle['close'] > prev_candle['open'] and  # Previous green
                    current_candle['close'] < current_candle['open'] and  # Current red
                    current_candle['close'] < prev_candle['open'] and  # Full engulf
                    current_candle['open'] > prev_candle['close'] and
                    volume_ratio > 1.1 and  # More flexible volume requirement
                    current_rsi < 65 and current_rsi > 15):  # More flexible RSI

                    entry_signals.append({
                        'type': 'strong_bearish_engulfing',
                        'strength': 35
                    })

                # 2. Bearish Pin Bar/Shooting Star (more flexible)
                body_size = abs(current_candle['close'] - current_candle['open'])
                total_size = current_candle['high'] - current_candle['low']
                upper_wick = current_candle['high'] - current_candle['close']

                if (body_size > 0 and total_size > body_size * 1.5 and  # More flexible rejection
                    upper_wick > body_size * 1.2 and  # More flexible wick requirement
                    current_candle['close'] < current_candle['open'] and  # Red candle
                    volume_ratio > 0.8 and  # More flexible volume
                    current_rsi < 70 and current_rsi > 20):  # More flexible RSI

                    entry_signals.append({
                        'type': 'bearish_shooting_star',
                        'strength': 30
                    })

                # 3. Bearish Momentum Breakdown
                if (current_candle['close'] < current_candle['open'] and  # Red candle
                    current_candle['close'] < prev_candle['low'] and  # Break previous low
                    current_rsi < 55 and current_rsi > 25):  # Good momentum

                    entry_signals.append({
                        'type': 'bearish_momentum',
                        'strength': 28
                    })

                # 4. Bearish Trend Continuation
                if (current_candle['close'] < current_candle['open'] and  # Red candle
                    current_candle['high'] < prev_candle['high'] and  # Lower high
                    current_rsi < 60 and current_rsi > 30 and  # Healthy RSI
                    volume_ratio > 0.9):  # Decent volume

                    entry_signals.append({
                        'type': 'bearish_continuation',
                        'strength': 25
                    })

                # 5. Three Black Crows Pattern
                if (len(df) >= 3 and
                    current_candle['close'] < current_candle['open'] and
                    prev_candle['close'] < prev_candle['open'] and
                    prev2_candle['close'] < prev2_candle['open'] and
                    current_candle['close'] < prev_candle['close'] < prev2_candle['close']):

                    entry_signals.append({
                        'type': 'three_black_crows',
                        'strength': 32
                    })

            # Enhanced trend following (balanced fallback)
            if not entry_signals and structure.get('structure_strength', 0) >= 60:
                if structure['bias'] == 'BULLISH' and current_rsi > 42 and current_rsi < 78:
                    entry_signals.append({
                        'type': 'basic_bullish_trend',
                        'strength': 23
                    })
                elif structure['bias'] == 'BEARISH' and current_rsi < 58 and current_rsi > 22:
                    entry_signals.append({
                        'type': 'basic_bearish_trend',
                        'strength': 23
                    })

            # Enhanced confluence checks
            confluence_score = 0

            # Check for support/resistance interaction (more flexible)
            for zone in zones.get('order_blocks', []):
                zone_level = zone['level']
                distance = abs(current_price - zone_level) / current_price

                if distance < 0.002:  # Close to zone (0.2%)
                    confluence_score += 12
                    entry_signals.append({
                        'type': 'zone_interaction',
                        'strength': 12
                    })
                elif distance < 0.005:  # Near zone (0.5%)
                    confluence_score += 8
                    entry_signals.append({
                        'type': 'near_zone_interaction',
                        'strength': 8
                    })

            # Check for liquidity zones interaction
            for zone in zones.get('liquidity_zones', []):
                zone_level = zone['level']
                distance = abs(current_price - zone_level) / current_price

                if distance < 0.003:  # Close to liquidity zone
                    confluence_score += 10
                    entry_signals.append({
                        'type': 'liquidity_zone_interaction',
                        'strength': 10
                    })

            # Check for trend continuation pattern (more flexible)
            if len(df) >= 5:
                trend_strength = self.calculate_trend_strength(df.tail(5))
                if trend_strength > self.trend_strength_threshold:  # Use configurable threshold
                    confluence_score += 12
                    entry_signals.append({
                        'type': 'trend_continuation',
                        'strength': 12
                    })

            # Check for momentum alignment
            if len(df) >= 3:
                momentum_score = self.calculate_momentum_score(df.tail(3))
                if momentum_score > self.momentum_threshold:
                    confluence_score += 8
                    entry_signals.append({
                        'type': 'momentum_alignment',
                        'strength': 8
                    })

            total_strength = sum(signal['strength'] for signal in entry_signals)

            # Balanced threshold for quality signals
            if total_strength < 20:
                return {'has_entry': False, 'entry_type': None, 'strength': total_strength}

            return {
                'has_entry': len(entry_signals) > 0,
                'entry_signals': entry_signals,
                'total_strength': total_strength,
                'entry_type': entry_signals[0]['type'] if entry_signals else None,
                'volume_confirmation': volume_ratio > 1.2,
                'rsi_healthy': 30 < current_rsi < 70
            }

        except Exception as e:
            self.logger.error(f"Error in enhanced entry confirmation: {e}")
            return {'has_entry': False, 'entry_type': None, 'strength': 0}

    def calculate_trend_strength(self, df: pd.DataFrame) -> float:
        """Calculate trend strength for confluence"""
        try:
            if len(df) < 3:
                return 0

            closes = df['close'].values
            trend_score = 0

            # Check for consistent direction
            for i in range(1, len(closes)):
                if closes[i] > closes[i-1]:
                    trend_score += 1
                elif closes[i] < closes[i-1]:
                    trend_score -= 1

            return abs(trend_score) / (len(closes) - 1)

        except Exception:
            return 0

    def calculate_momentum_score(self, df: pd.DataFrame) -> float:
        """Calculate momentum score for confluence"""
        try:
            if len(df) < 2:
                return 0

            # Calculate price momentum
            price_change = (df['close'].iloc[-1] - df['close'].iloc[0]) / df['close'].iloc[0]

            # Calculate volume momentum
            volume_change = df['volume'].iloc[-1] / df['volume'].mean()

            # Combine price and volume momentum
            momentum_score = abs(price_change) * min(volume_change, 2.0)  # Cap volume impact

            return min(momentum_score, 1.0)  # Normalize to 0-1

        except Exception:
            return 0

    def calculate_market_volatility(self, df: pd.DataFrame) -> float:
        """Calculate market volatility to filter choppy conditions"""
        try:
            if len(df) < 10:
                return 0.5  # Default neutral value

            # Calculate price range volatility
            high_low_range = (df['high'] - df['low']) / df['close']
            avg_range = high_low_range.tail(10).mean()

            # Calculate directional movement
            price_changes = df['close'].pct_change().tail(10)
            directional_movement = abs(price_changes.sum()) / price_changes.abs().sum()

            # Combine range and directional movement
            volatility_score = avg_range * directional_movement * 10  # Scale up

            return min(volatility_score, 1.0)  # Normalize to 0-1

        except Exception:
            return 0.5  # Default neutral value

    def generate_smc_signal(self, structure: Dict, zones: Dict, entry: Dict, df: pd.DataFrame) -> Dict:
        """
        Enhanced Signal Generation with strict quality control
        Only generates signals for perfect setups
        """
        try:
            if not entry.get('has_entry', False):
                return {'signal': 0, 'confidence': 0}

            current_price = df['close'].iloc[-1]

            # Enhanced confidence calculation with more flexible scoring
            confidence = 0

            # Structure strength scoring (more generous)
            structure_strength = structure.get('structure_strength', 0)
            if structure_strength >= 85:
                confidence += 30
            elif structure_strength >= 75:
                confidence += 25
            elif structure_strength >= 65:
                confidence += 20
            elif structure_strength >= 50:
                confidence += 15
            else:
                confidence += 10  # Give points even for weaker structures

            # Entry signals scoring (more generous)
            entry_strength = entry.get('total_strength', 0)
            if entry_strength >= 35:
                confidence += 35
            elif entry_strength >= 30:
                confidence += 30
            elif entry_strength >= 25:
                confidence += 25
            elif entry_strength >= 20:
                confidence += 20
            elif entry_strength >= 18:
                confidence += 15
            else:
                confidence += 10  # Give points for basic entries

            # Volume confirmation bonus
            if entry.get('volume_confirmation', False):
                confidence += 12
            elif entry.get('volume_confirmation') is not False:  # Any volume data
                confidence += 8

            # RSI health bonus
            if entry.get('rsi_healthy', False):
                confidence += 10
            elif 25 < structure.get('rsi', 50) < 75:  # Reasonable RSI
                confidence += 5

            # EMA alignment bonus
            if structure.get('ema_alignment') in ['BULLISH_ALIGNED', 'BEARISH_ALIGNED']:
                confidence += 15
            elif structure.get('ema_alignment') == 'NOT_ALIGNED':
                confidence += 5  # Still give some points

            # Market session bonus (add later if needed)
            confidence += 5  # Base market participation bonus

            # Only generate signal if confidence meets threshold (lowered)
            if confidence < self.min_confidence:
                return {'signal': 0, 'confidence': confidence}

            # Determine direction
            bias = structure.get('bias', 'NEUTRAL')
            if bias == 'NEUTRAL':
                return {'signal': 0, 'confidence': confidence}

            direction = 'LONG' if bias == 'BULLISH' else 'SHORT'

            # Enhanced adaptive risk management
            entry_price = current_price

            # Use swing points for better SL placement
            swing_highs = structure.get('swing_highs', [])
            swing_lows = structure.get('swing_lows', [])

            atr = self.calculate_atr(df)

            # Adaptive volatility multiplier based on confidence
            volatility_multiplier = self.volatility_multiplier
            if confidence >= 80:
                volatility_multiplier *= 0.8  # Tighter stops for high confidence
            elif confidence >= 60:
                volatility_multiplier *= 1.0  # Normal stops
            else:
                volatility_multiplier *= 1.2  # Wider stops for lower confidence

            if direction == 'LONG':
                # Adaptive SL placement
                if swing_lows and len(swing_lows) >= 2:
                    recent_low = min(swing_lows[-2:])  # Last 2 swing lows
                    stop_loss = recent_low - (atr * volatility_multiplier * 0.5)
                elif swing_lows:
                    recent_low = swing_lows[-1]
                    stop_loss = recent_low - (atr * volatility_multiplier * 0.7)
                else:
                    stop_loss = entry_price - (atr * volatility_multiplier * 1.5)

                # Adaptive TP calculation based on entry strength
                sl_distance = entry_price - stop_loss

                # Dynamic RR based on entry quality
                if entry_strength >= 35:
                    rr_ratio = self.risk_reward_max * 0.8  # High quality = higher target
                elif entry_strength >= 25:
                    rr_ratio = self.risk_reward_min * 1.5  # Medium quality
                else:
                    rr_ratio = self.risk_reward_min  # Basic quality

                take_profit = entry_price + (sl_distance * rr_ratio)

            else:  # SHORT
                # Adaptive SL placement
                if swing_highs and len(swing_highs) >= 2:
                    recent_high = max(swing_highs[-2:])  # Last 2 swing highs
                    stop_loss = recent_high + (atr * volatility_multiplier * 0.5)
                elif swing_highs:
                    recent_high = swing_highs[-1]
                    stop_loss = recent_high + (atr * volatility_multiplier * 0.7)
                else:
                    stop_loss = entry_price + (atr * volatility_multiplier * 1.5)

                # Adaptive TP calculation
                sl_distance = stop_loss - entry_price

                # Dynamic RR based on entry quality
                if entry_strength >= 35:
                    rr_ratio = self.risk_reward_max * 0.8
                elif entry_strength >= 25:
                    rr_ratio = self.risk_reward_min * 1.5
                else:
                    rr_ratio = self.risk_reward_min

                take_profit = entry_price - (sl_distance * rr_ratio)

            # Validate risk-reward ratio (more flexible)
            if direction == 'LONG':
                actual_rr = (take_profit - entry_price) / (entry_price - stop_loss)
            else:
                actual_rr = (entry_price - take_profit) / (stop_loss - entry_price)

            # Balanced RR requirement
            min_rr = 1.4 if confidence >= 70 else 1.6  # Balanced RR requirements
            if actual_rr < min_rr:
                return {'signal': 0, 'confidence': confidence}

            # Determine timeframe based on entry quality
            timeframe = 'M5'
            entry_type = entry.get('entry_type', '')
            if 'perfect' in entry_type or entry_strength >= 35:
                timeframe = 'M1'  # Higher precision for high quality setups
            elif entry_strength >= 25:
                timeframe = 'M3'  # Medium precision for good setups

            # Enhanced entry zone description
            entry_zone_desc = f"Enhanced {direction} Setup"
            if entry_strength >= 35:
                entry_zone_desc = f"High Quality {direction} Setup"
            elif entry_strength >= 25:
                entry_zone_desc = f"Good {direction} Setup"
            elif entry_strength >= 20:
                entry_zone_desc = f"Decent {direction} Setup"

            return {
                'signal': 1 if direction == 'LONG' else -1,
                'entry_price': entry_price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'direction': direction,
                'confidence': min(confidence, 100),
                'timeframe': timeframe,
                'entry_zone': entry_zone_desc,
                'risk_reward': actual_rr,
                'entry_strength': entry_strength,
                'structure_strength': structure_strength,
                'volatility_adjusted': True
            }

        except Exception as e:
            self.logger.error(f"Error generating enhanced signal: {e}")
            return {'signal': 0, 'confidence': 0}

    def calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range for volatility-based SL"""
        try:
            if len(df) < period + 1:
                return df['high'].iloc[-1] - df['low'].iloc[-1]

            # Create a copy to avoid warnings
            df_copy = df.copy()

            high = df_copy['high']
            low = df_copy['low']
            close = df_copy['close'].shift(1)

            tr1 = high - low
            tr2 = abs(high - close)
            tr3 = abs(low - close)

            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = tr.rolling(window=period).mean().iloc[-1]

            return atr if not pd.isna(atr) else (df['high'].iloc[-1] - df['low'].iloc[-1])

        except Exception:
            return df['high'].iloc[-1] - df['low'].iloc[-1]

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except Exception:
            return pd.Series([50] * len(prices), index=prices.index)

    def calculate_macd(self, prices: pd.Series) -> pd.Series:
        """Calculate MACD indicator"""
        try:
            ema_12 = prices.ewm(span=12).mean()
            ema_26 = prices.ewm(span=26).mean()
            macd = ema_12 - ema_26
            return macd
        except Exception:
            return pd.Series([0] * len(prices), index=prices.index)

    def identify_swing_points(self, df: pd.DataFrame) -> Tuple[List[float], List[float]]:
        """Identify swing highs and lows with precision"""
        try:
            highs = []
            lows = []

            # Use a larger window for more significant swings
            window = 7

            for i in range(window, len(df) - window):
                # Check for swing high
                if (df['high'].iloc[i] == df['high'].iloc[i-window:i+window+1].max() and
                    df['high'].iloc[i] > df['high'].iloc[i-1] and
                    df['high'].iloc[i] > df['high'].iloc[i+1]):
                    highs.append(df['high'].iloc[i])

                # Check for swing low
                if (df['low'].iloc[i] == df['low'].iloc[i-window:i+window+1].min() and
                    df['low'].iloc[i] < df['low'].iloc[i-1] and
                    df['low'].iloc[i] < df['low'].iloc[i+1]):
                    lows.append(df['low'].iloc[i])

            return highs[-5:], lows[-5:]  # Return last 5 swings

        except Exception:
            return [], []

    def check_ema_alignment(self, ema_21: float, ema_50: float, ema_200: float) -> str:
        """Check EMA alignment for trend confirmation"""
        try:
            if ema_21 > ema_50 > ema_200:
                return 'BULLISH_ALIGNED'
            elif ema_21 < ema_50 < ema_200:
                return 'BEARISH_ALIGNED'
            else:
                return 'NOT_ALIGNED'
        except Exception:
            return 'NOT_ALIGNED'
