# 🚀 Advanced Crypto Trading Bot

**Professional real-time crypto trading bot with minute-by-minute analysis and comprehensive backtesting system.**

## ✨ Features

### 🔴 **Real-time Analysis**
- **Analyzes 20 crypto pairs every minute** (e.g., 13:45, 13:46, 13:47)
- **Multi-timeframe strategy** (1M, 5M, 15M)
- **Professional signal format** with Entry/SL/TP/Direction/Confidence/Reason/Timeframe
- **No-signal format** with Price/Bias/Potential Entry/Message
- **Beautiful colorful CLI** output

### 🧪 **Backtest System**
- **User-friendly input** for pair selection and time range
- **Historical 5M data** from Binance API
- **Comprehensive tracking** (Trades/Wins/Losses/Points/P&L)
- **Professional results** with ✅ PROFIT/❌ LOSS indicators

### 📊 **Monitored Pairs**
ADA/USDT, XRP/USDT, AVAX/USDT, MATIC/USDT, LTC/USDT, DOGE/USDT, SHIB/USDT, BNB/USDT, ARB/USDT, APT/USDT, OP/USDT, INJ/USDT, LINK/USDT, ATOM/USDT, FIL/USDT, SAND/USDT, GALA/USDT, NEAR/USDT, ALGO/USDT, AAVE/USDT

## 🚀 Quick Start

### **Installation**
```bash
pip install requests pandas numpy schedule
```

### **Run the Bot**
```bash
python main.py
```

## 📋 Usage

### **Option 1: Real-time Analysis**
1. Run: `python main.py`
2. Select: `1`
3. Confirm: `y`
4. Bot analyzes all 20 pairs every minute
5. Press `Ctrl+C` to stop

### **Option 2: Backtest System**
1. Run: `python main.py`
2. Select: `2`
3. Confirm: `y`
4. Enter pairs: `1,2,3` (for ADA, XRP, AVAX)
5. Start time: `2023-12-01 00:00`
6. End time: `2023-12-10 23:59`

## 📁 Essential Files

- `main.py` - Main launcher
- `trading_bot.py` - Real-time analysis
- `backtest_system.py` - Backtesting
- `multi_timeframe_strategy.py` - Trading strategy
- `market_structure_analyzer.py` - Market analysis
- `liquidity_zone_detector.py` - Liquidity detection
- `m5_entry_detector.py` - Entry confirmation
- `requirements.txt` - Dependencies
- `README.md` - Documentation
- `HOW_TO_RUN.md` - Instructions

## ⚡ Quick Commands

### **Real-time Analysis**
```bash
python -c "from trading_bot import CryptoTradingBot; bot = CryptoTradingBot(); bot.start_real_time_analysis()"
```

### **Backtest**
```bash
python -c "from backtest_system import BacktestSystem; BacktestSystem().run_backtest()"
```

## 🔧 Requirements

- Python 3.7+
- Internet connection (Binance API)
- Terminal/Command Prompt

## 🎊 Ready to Trade!

**Start trading now:**
```bash
python main.py
```

---

*Happy Trading! 🚀📈✨*
