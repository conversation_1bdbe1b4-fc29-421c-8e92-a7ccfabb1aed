# 🚀 HOW TO RUN YOUR CRYPTO TRADING BOT

## 📋 **QUICK START GUIDE**

### **Step 1: Open Terminal/Command Prompt**
Navigate to your bot directory:
```bash
cd C:\Users\<USER>\OneDrive\Desktop\CryptoSignalBot
```

### **Step 2: Choose Your Mode**

## 🔴 **OPTION A: LIVE REAL-TIME ANALYSIS**

### **Command:**
```bash
python main.py
```

### **Steps:**
1. **Run the command above**
2. **Select option `1`** when menu appears
3. **Type `y`** to confirm starting real-time analysis
4. **<PERSON><PERSON> will start analyzing all 20 pairs every minute**

### **What You'll See:**
```
📊 MARKET ANALYSIS - 15:45
======================================================================

[01/20] Analyzing ADA/USDT...
[ADA/USDT] LONG SIGNAL 🔔
  Entry Price: $0.6709
  Stop Loss: $0.6575
  Take Profit: $0.6975
  Direction: Long
  Confidence: 78%
  Reason: Liquidity Sweep + CHoCH + Order Block
  Entry Timeframe: M5

[02/20] Analyzing XRP/USDT...
[XRP/USDT]
  Current Price: $2.1350
  Direction Bias: BEARISH
  Potential Entry: $2.0850
  No entry currently.

[03/20] Analyzing AVAX/USDT...
...continues for all 20 pairs...

📊 Analysis Summary: 2 signals found | Total: 15 signals
======================================================================
```

### **Features:**
- ✅ **Runs every minute** at minute marks (15:45, 15:46, etc.)
- ✅ **Analyzes all 20 crypto pairs**
- ✅ **Shows live signals** with Entry/SL/TP/Direction/Confidence/Reason/Timeframe
- ✅ **Shows no-signal status** with Price/Bias/Potential Entry
- ✅ **Colorful CLI output**
- ✅ **Press Ctrl+C to stop**

---

## 🧪 **OPTION B: BACKTEST SYSTEM**

### **Command:**
```bash
python main.py
```

### **Steps:**
1. **Run the command above**
2. **Select option `2`** when menu appears
3. **Type `y`** to confirm starting backtest
4. **Follow the prompts:**

### **Input Example:**
```
📊 Available Crypto Pairs:
   1. ADA/USDT    11. OP/USDT
   2. XRP/USDT    12. INJ/USDT
   3. AVAX/USDT   13. LINK/USDT
   4. MATIC/USDT  14. ATOM/USDT
   5. LTC/USDT    15. FIL/USDT
   ...

Select crypto pairs (comma-separated numbers, e.g., 1,3,5): 1,2,3
Enter Start Time (e.g., "2023-12-01 00:00"): 2023-12-01 00:00
Enter End Time (e.g., "2023-12-10 23:59"): 2023-12-10 23:59
```

### **What You'll See:**
```
✅ Backtest Configuration:
📊 Pairs: ADA/USDT, XRP/USDT, AVAX/USDT
📅 Period: 2023-12-01 00:00 to 2023-12-10 23:59
⏱️ Duration: 9 days

🚀 Starting backtest...
============================================================

🔍 Backtesting ADA/USDT (1/3)...
📊 Analyzing 2,592 M5 candles...

[ADA/USDT Backtest]
- Total Trades: 17
- Wins: 12
- Losses: 5
- Points Gained: +1035
- Points Lost: -285
- Net P/L: +750 ✅ PROFIT

🔍 Backtesting XRP/USDT (2/3)...
📊 Analyzing 2,592 M5 candles...

[XRP/USDT Backtest]
- Total Trades: 23
- Wins: 14
- Losses: 9
- Points Gained: +892
- Points Lost: -456
- Net P/L: +436 ✅ PROFIT

📊 BACKTEST SUMMARY 📊
============================================================
📊 Total Pairs Tested: 3
📈 Total Trades: 59
✅ Total Wins: 34
❌ Total Losses: 25
📊 Win Rate: 57.6%
🎯 Overall P/L: +1051 ✅ PROFIT
============================================================
```

---

## 🎯 **ALTERNATIVE METHODS**

### **Method 2: Direct Real-time Analysis**
```bash
python -c "from trading_bot import CryptoTradingBot; bot = CryptoTradingBot(); bot.start_real_time_analysis()"
```

### **Method 3: Direct Backtest**
```bash
python -c "from backtest_system import BacktestSystem; backtest = BacktestSystem(); backtest.run_backtest()"
```

### **Method 4: Demo Mode**
```bash
python run_bot.py
```

---

## 📊 **WHAT EACH MODE DOES**

### **🔴 Real-time Analysis:**
- **Frequency**: Every minute (e.g., 13:45, 13:46, 13:47)
- **Pairs**: All 20 crypto pairs analyzed simultaneously
- **Data**: Live market data from Binance API
- **Strategy**: Multi-timeframe analysis (1M, 5M, 15M)
- **Output**: Signal alerts OR no-signal status for each pair
- **Duration**: Runs continuously until you stop it (Ctrl+C)

### **🧪 Backtest System:**
- **Input**: You choose pairs and time range
- **Data**: Historical 5M data from Binance
- **Strategy**: Same strategy applied to historical data
- **Tracking**: Trades, Wins, Losses, Points Gained/Lost, Net P/L
- **Output**: Detailed results for each pair + summary
- **Duration**: Runs once and shows results

---

## 🎮 **CONTROLS**

### **During Real-time Analysis:**
- **Ctrl+C**: Stop the bot gracefully
- **Bot runs automatically**: No other input needed

### **During Backtest:**
- **Follow prompts**: Enter pairs, start time, end time
- **Wait for results**: Bot will process and show results
- **Return to menu**: Choose y/n to return to main menu

---

## 🔧 **TROUBLESHOOTING**

### **If bot doesn't start:**
1. Make sure you're in the correct directory
2. Check if Python is installed: `python --version`
3. Install required packages: `pip install requests pandas numpy schedule`

### **If you see errors:**
1. Check internet connection (bot needs Binance API access)
2. Make sure all files are present: `main.py`, `trading_bot.py`, `backtest_system.py`
3. Check the log file: `trading_bot.log`

---

## 🎯 **READY TO START?**

### **For Live Analysis:**
```bash
python main.py
# Select 1, then y
```

### **For Backtesting:**
```bash
python main.py
# Select 2, then y, then follow prompts
```

**Your advanced crypto trading bot is ready to hunt for signals!** 🚀📈✨
